{"name": "detect-gpu", "version": "5.0.70", "description": "Classify GPU's based on their benchmark score in order to provide an adaptive experience.", "author": "<PERSON>", "license": "MIT", "main": "dist/detect-gpu.umd.js", "module": "dist/detect-gpu.esm.js", "types": "dist/src/index.d.ts", "homepage": "https://github.com/pmndrs/detect-gpu#readme", "bugs": {"url": "https://github.com/pmndrs/detect-gpu/issues"}, "repository": {"type": "git", "url": "https://github.com/pmndrs/detect-gpu.git"}, "files": ["dist"], "keywords": ["gpu", "detect", "webgl", "webgl2", "three.js", "<PERSON><PERSON><PERSON><PERSON>", "three", "babylon", "3d", "typescript", "javascript"], "scripts": {"start": "rollup -c rollup/config.lib.ts -w --configPlugin rollup-plugin-typescript2", "lint": "eslint \"src/**/*.ts\" \"test/**/*.ts\" \"test/**/*.test.ts\" \"rollup/**/*.ts\" \"scripts/**/*.ts\" \"scripts/**/*.js\" --fix --cache --cache-location ~/.eslintcache/eslintcache", "test": "jest --verbose=false", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:debug": "node --inspect-brk ./node_modules/jest/bin/jest --runInBand --no-cache --watch", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c rollup/config.lib.ts --configPlugin rollup-plugin-typescript2", "example": "rollup -w -c rollup/config.dev.ts --configPlugin rollup-plugin-typescript2", "parse-analytics": "node ./scripts/analytics_parser.js", "update-benchmarks": "rimraf benchmarks && mkdir -p benchmarks && mkdir -p benchmarks-min && ts-node -O '{\"module\":\"commonjs\"}' ./scripts/update_benchmarks.ts && tar -czvf benchmarks.tar.gz benchmarks-min/*.json && rm -rf benchmarks-min"}, "dependencies": {"webgl-constants": "^1.1.1"}, "devDependencies": {"@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.1.0", "@types/jest": "^29.5.3", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "csvtojson": "^2.0.10", "eslint": "^8.4.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.6.2", "jest-environment-jsdom": "^29.6.2", "moment": "^2.29.1", "prettier": "^3.0.1", "puppeteer": "^13.0.0", "rimraf": "^5.0.1", "rollup": "^3.28.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-copy": "^3.4.0", "rollup-plugin-filesize": "^10.0.0", "rollup-plugin-livereload": "^2.0.0", "rollup-plugin-serve": "^2.0.2", "rollup-plugin-sourcemaps": "^0.6.3", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.1", "ts-jest": "^29.1.1", "ts-node": "^10.0.0", "typescript": "^5.1.6"}, "jest": {"testEnvironmentOptions": {"url": "http://localhost"}, "moduleFileExtensions": ["js", "ts"], "testMatch": ["**/test/**/*.test.ts"], "testPathIgnorePatterns": ["<rootDir>/test/data.ts"], "preset": "ts-jest", "transform": {"^.+\\.tsx?$": ["ts-jest", {"tsconfig": "tsconfig.json"}]}}}