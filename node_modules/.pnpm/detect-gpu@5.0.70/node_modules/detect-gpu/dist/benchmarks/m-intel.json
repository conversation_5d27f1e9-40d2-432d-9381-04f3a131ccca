["5", ["intel intel hd graphics for atom x5", "5", "atom for graphics hd intel x5", 0, [[1024, 600, 35, "amazon aeokn"], [1024, 720, 13, "arbor venus 8"], [1217, 720, 23, "gole gole1"], [1280, 736, 27, "medion p850x"], [1360, 704, 15, "chuwi hibox hero mini pc"], [1280, 752, 21, "tetratab casebook 3"], [1280, 759, 28, "lenovo yoga a12 (yb-q501f)"], [1366, 720, 19, "nextbook nx16a11264 ares 11 (x5-z8300)"], [1920, 996, 20, "teclast x16 pro"], [1920, 1008, 12, "teclast tbook 16 pro"], [1920, 1016, 13, "teclast x16 plus"], [1920, 1104, 13, "medion p851x"], [1920, 1116, 13, "teclast x80 pro (x5-z8350)"], [1920, 1128, 8, "cube technology i12-y"], [1920, 1133, 9, "cube technology i1-tfp (x5-z8350)"], [1920, 1134, 11, "microtech e-tab pro lte"], [1920, 1136, 14, "cube technology iwork 10 ultimate (i15-t)"], [1920, 1214, 10, "chuwi cw-hi10 plus (x5-z8350)"], [2048, 1440, 8, "teclast x98 plus"], [2160, 1368, 10, "chuwi hi12 (x5-z8350)"], [2048, 1452, 10, "teclast x98 plus ii"], [2048, 1536, 14, "xiaomi mi pad 2"], [2560, 1356, 7, "baofeng a1"], [2560, 1504, 6, "chuwi hibook pro (z8350)"], [2560, 1518, 11, "lenovo yt3-x90 yoga tablet 3 pro (x5-z8550)"]]], ["intel intel hd graphics for baytrail", "hdfor", "baytrail for graphics hd intel", 0, [[1024, 552, 13, "multilaser intel 7qc"], [1024, 696, 17, "thundersoft dual os tablet"], [1024, 720, 15, "airis onepad 785i"], [1184, 720, 18, "trimble tdc500"], [1280, 736, 12, "acer a1-840"], [1280, 752, 9, "imuz mupad win 10.1 ii"], [1366, 720, 13, "nextbook nxa116qc164"], [1366, 724, 10, "cube technology i10 remix"], [1440, 1008, 12, "teclast x89 kindow"], [1920, 1008, 8, "minix neo z64"], [1920, 1014, 9, "cube technology i7 cx remix"], [1920, 1032, 11, "hp slate 17"], [1920, 1104, 5, "jltd d630"], [1920, 1128, 6, "intel(r) e1008"], [1920, 1133, 9, "pipo w3f"], [2048, 1440, 6, "kruger & matz eagle 975 (km0975)"], [2048, 1448, 6, "jide e-tab 3g"], [2048, 1464, 6, "reeder a10ix air"], [2560, 1344, 7, "lenovo yoga tablet 2 pro-1380"], [2560, 1504, 5, "teclast x10hd 3g"]]], ["intel mesa dri intel bay trail", "dribay", "bay dri intel mesa trail", 0, [[1366, 768, 15, "google chromebook pixel (2015, n2830)"]]], ["intel mesa dri intel hd", "drihd", "dri hd intel mesa", 0, [[688, 412, 39, "hp chromebook x360 11 g1 ee"], [960, 568, 36, "google chromebook pixel (2015, n3450)"], [1024, 561, 28, "aaeon up-cht01 up board"], [1024, 736, 33, "google chromebook reef (n3350)"]]], ["intel mesa dri intel hd graphics 400", "400", "400 dri graphics hd intel mesa", 0, [[688, 412, 41, "asus c202sa chromebook"], [768, 431, 45, "hp chromebook 11 g5"], [960, 568, 25, "acer chromebook r11 (n3050)"], [1366, 768, 19, "samsung chromebook 3"], [1536, 832, 25, "google chromebook r11 (n3160)"], [1920, 1080, 17, "acer chromebook 14"]]], ["intel mesa dri intel hd graphics 510", "510", "510 dri graphics hd intel mesa", 0, [[690, 378, 53, "hp chromebook chell"], [688, 412, 60, "acer chromebook 14 for work"]]], ["intel mesa dri intel hd graphics 515", "515", "515 dri graphics hd intel mesa", 0, [[1033, 617, 56, "samsung chromebook pro (m7-6y75, caroline)"], [1536, 1088, 32, "google chromebook pixel (2015, m3-6y30)"], [1920, 980, 32, "asus c302 chromebook flip"], [2400, 1504, 22, "samsung chromebook pro (caroline)"], [3200, 1640, 12, "google chromebook pixel (2015, 4405y)"]]], ["intel mesa dri intel hd graphics 520", "520", "520 dri graphics hd intel mesa", 0, [[700, 412, 60, "acer chromebook 14 for work (i3-6100u)"]]], ["intel mesa dri intel hd graphics 5500", "5500", "5500 dri graphics hd intel mesa", 0, [[1080, 575, 59, "google chromebook pixel (2015, i3-5005u)"], [1920, 1000, 30, "google chromebook pixel (2015, i5-5300u)"], [2560, 1700, 14, "google chromebook pixel (2015, i5-5200u)"]]], ["intel mesa dri intel hd graphics 615", "615", "615 dri graphics hd intel mesa", 0, [[1034, 618, 55, "google soraka (4415y)"], [1200, 720, 34, "google poppy (4410y, kabylake)"], [2400, 1504, 21, "google chromebook eve (i5-7y54)"]]], ["intel mesa dri intel kabylake gt2", "2", "dri gt2 intel kabylake mesa", 0, [[960, 568, 38, "google chromebook pixel (2015, m3-7y30)"]]]]