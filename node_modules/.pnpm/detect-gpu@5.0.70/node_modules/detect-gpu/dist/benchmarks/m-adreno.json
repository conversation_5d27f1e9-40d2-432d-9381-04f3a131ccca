["5", ["adreno 506", "506", "506 adreno", 0, [[1512, 720, 18, "motorola moto g(7) play"]]], ["adreno 530", "530", "530 adreno", 0, [[2560, 1140, 27, "samsung galaxy s7 (sm-930x)"], [2392, 1440, 28, "google pixel xl"]]], ["adreno 615", "615", "615 adreno", 0, [[2088, 1080, 20, "google pixel 3a"]]], ["adreno 618", "618", "618 adreno", 0, [[2210, 1080, 34, "xiaomi mi 9t"]]], ["adreno 620", "620", "620 adreno", 0, [[2340, 1080, 42, "google pixel 5"]]], ["adreno 630", "630", "630 adreno", 0, [[2280, 1080, 58, "oneplus a6003"]]], ["adreno 640", "640", "640 adreno", 0, [[2280, 1080, 60, "google pixel 4"]]], ["qualcomm adreno 205", "205", "205 adreno qualcomm", 0, [[1280, 720, 11, "coolpad 8675-w00 (adreno 205)"]]], ["qualcomm adreno 304", "304", "304 adreno qualcomm", 0, [[480, 320, 16, "zte z353vl cymbal t"], [480, 480, 6, "lg watch sport"], [758, 480, 8, "kyocera kyf33 torque x01 s-max"], [782, 480, 8, "vodafone vfd 510"], [791, 480, 8, "acer t012"], [792, 480, 6, "lg k4 2017 (m151, m160, m150 phoenix 3, m153 fortune, m154 risio 2, l58vl rebel 2)"], [800, 480, 7, "chainway c4050-q4"], [854, 480, 6, "zte q302c"], [897, 540, 6, "kyocera c6742 hydro view"], [960, 540, 6, "cat s40"], [1024, 552, 6, "tcl 9007 pixi 3 (7)"], [1184, 720, 4, "micromax q4251 canvas juice a1"], [1187, 720, 4, "lg phoenix 2 (k37x)"], [1193, 720, 4, "lg x power (k210, k450)"], [1196, 720, 4, "obi sj2.5"], [1217, 720, 4, "fibo la0920"], [1224, 720, 4, "umax visionbook p70 lte"], [1280, 720, 4, "k-touch beeline fast +"], [1280, 736, 4, "alcatel 9022x one touch pixi 3 8.0"], [1280, 752, 4, "lenovo tab 10 (x103f)"]]], ["qualcomm adreno 305", "305", "305 adreno qualcomm", 0, [[432, 320, 18, "sonim xp6700"], [480, 320, 16, "lg t480s wine smart 3g"], [400, 400, 0, "huawei watch"], [734, 480, 7, "lg l fino (d290, d295)"], [737, 480, 7, "acer liquid z220"], [790, 480, 8, "htc desire 510 (adreno 305)"], [791, 480, 7, "sony xperia m (c19xx, c20xx)"], [800, 480, 5, "cherry mobile q510"], [854, 480, 6, "alcatel a564c one touch pop icon"], [886, 540, 7, "lg g2 mini (d610, d618, d620)"], [896, 540, 7, "htc desire 610"], [897, 540, 5, "motorola xt830c moto e"], [960, 540, 4, "huawei c8817l"], [1024, 528, 5, "aurora au701"], [1024, 552, 4, "asus zenfone go 6.9 (l001 zb690kg)"], [982, 600, 4, "huawei mediapad 7 youth 2"], [1024, 600, 7, "samsung galaxy tab 3 7.0 (sm-t215, sm-t217)"], [1024, 720, 4, "ereneben eben a2"], [1024, 722, 5, "prestigio pmt5287 multipad ranger 8.0 4g"], [1280, 648, 5, "tomtom bridge"], [1184, 720, 5, "htc desire 650 (d650)"], [1188, 720, 4, "lg aka (h778, h788, f520x)"], [1196, 720, 3, "zte z787"], [1200, 720, 4, "lg g vista (d631, vs880, gx2 f430)"], [1216, 720, 4, "vodafone smart 4 max"], [1217, 720, 4, "kyocera e6790 duraforce xd"], [1220, 720, 4, "h<PERSON><PERSON> ascend mate 2 mt2-l03"], [1196, 768, 4, "infocus m2"], [1280, 720, 3, "zuum p47"], [1280, 732, 4, "lg g pad 7.0 (uk410, v410, vk410)"], [1280, 736, 4, "zte amazing p6"], [1280, 737, 4, "asus memo pad hd 7 (k00s me175kg)"], [1280, 739, 4, "lg g pad 8.0 (v480, v49x, p490, t01)"], [1280, 740, 4, "qualcomm msm8926 (adreno 305, development board)"], [1280, 752, 4, "asus padfone e (t008 a68m)"], [1216, 800, 3, "huawei mediapad t1 8.0 (adreno 305)"], [1280, 800, 3, "samsung tn10gedlte"], [1920, 1080, 2, "zte s2002 star 1"], [1920, 1128, 2, "lenovo b8080"]]], ["qualcomm adreno 306", "306", "306 adreno qualcomm", 0, [[734, 480, 9, "lg f60 (d390, d392)"], [790, 480, 8, "htc desire 510 (adreno 306)"], [791, 480, 5, "lg l21g destiny"], [800, 480, 8, "covia fleaz cp-l42a pop"], [854, 480, 8, "freetel ft151a priori2 lte"], [897, 540, 7, "kyocera c6740 hydro wave"], [960, 540, 5, "blu studio mini lte 2"], [1024, 552, 6, "tcl 9006w"], [1024, 600, 5, "samsung sm-t116ir (adreno 306)"], [1024, 720, 5, "ereneben eben k8s"], [1024, 768, 5, "samsung sm-p350 galaxy tab a plus"], [1184, 720, 4, "lg x screen (k500, f650)"], [1188, 720, 4, "lg band play (f570s)"], [1196, 720, 4, "fly a5042"], [1199, 720, 4, "lg stylus 2 (f720, k520, k540, ls775 g stylo 2, l82vl)"], [1200, 720, 4, "lg g4 stylus (h63x, ms631, f560, ls770 g stylo)"], [1208, 720, 4, "vizio xr6m10 tablet remote"], [1217, 720, 4, "lenovo pb1-750x phab"], [1224, 720, 4, "c spire ft7"], [1280, 720, 3, "bgh joy smart axs ii"], [1280, 736, 4, "vodafone smart tab 4g"], [1280, 752, 4, "kyocera smile tablet 3 (szj-js201)"], [1216, 800, 4, "huawei mediapad t1 8.0 pro, lte (adreno 306)"], [1280, 800, 4, "samsung galaxy tab e 8.0 (adreno 306, sm-t377p)"], [1794, 1080, 2, "bluebird sf550"], [1815, 1080, 2, "vizio xr6"], [1920, 1080, 2, "longcheer cc01"]]], ["qualcomm adreno 308", "308", "308 adreno qualcomm", 0, [[800, 480, 13, "samsung galaxy folder 2 (sm-g160x)"], [854, 480, 12, "coolpad 3632"], [960, 540, 10, "samsung sm-j250g"], [1184, 720, 5, "infocus 00ww"], [1187, 720, 6, "lg m200"], [1193, 720, 6, "lg k20 plus (mp260, k20 v vs501)"], [1195, 720, 6, "lg x charge (x power 2, m322, l63bl fiesta)"], [1199, 720, 6, "lg stylo 3 (l83bl)"], [1280, 720, 6, "hisense f23"], [1280, 736, 6, "lenovo tb-8504"], [1280, 752, 6, "lenovo tb-x304"], [1344, 720, 6, "wiko view"], [1223, 800, 6, "huawei mediapad t3 8.0 (kob-xxx)"], [1368, 720, 6, "casper via g1"], [1280, 800, 6, "samsung galaxy tab a2 s (sm-t380, sm-t385)"]]], ["qualcomm adreno 320", "320", "320 adreno qualcomm", 0, [[1196, 720, 11, "pantech im-a850 vega r3"], [1196, 768, 10, "google nexus 4 (lg e960)"], [1280, 720, 8, "blackberry z30"], [1280, 752, 8, "asus memo pad 10 (k01e me103k)"], [1280, 768, 10, "lg optimus g (e97x, ls970, e987, f180, kddi lgl21)"], [1920, 1008, 5, "technicolor px36"], [1794, 1080, 5, "sony xperia z (c66xx, so-02e, l36x)"], [1848, 1080, 5, "pantech im-a860 vega n6"], [1920, 1080, 5, "zte nubia z5 (nx501, nx50x)"], [1920, 1104, 5, "google nexus 7 (2nd gen, razor)"], [1920, 1114, 4, "lg g pad 8.3 (vk810 4g)"], [1920, 1128, 5, "sony xperia tablet z (sgp3xx, so-03e)"]]], ["qualcomm adreno 330", "330", "330 adreno qualcomm", 0, [[800, 480, 18, "samsung sm-w2014"], [1184, 720, 27, "sony so-04g xperia a4"], [1196, 720, 21, "sony xperia j1 compact (d5788)"], [1202, 720, 21, "lg g flex (d95x, ls995, lgl23, f340)"], [1280, 720, 20, "amazon fire phone (sd4930ur)"], [1280, 768, 21, "samsung sm-w2015 galaxy golden 2"], [1280, 960, 14, "lg f300 optimus vu 3"], [1600, 1152, 15, "hp pro slate 12"], [1440, 1308, 10, "blackberry passport"], [1776, 1080, 11, "google nexus 5"], [1788, 1080, 11, "lg g3 a f410"], [1794, 1080, 10, "fujitsu f-01f arrows nx luge (docomo)"], [1803, 1080, 11, "lg g pro 2 (d838, f350)"], [1836, 1080, 11, "sony xperia z ultra (c68xx, xl39h, sol24, sgp412)"], [1920, 1080, 9, "qualcomm quanta is7"], [1920, 1104, 12, "ntt docomo sh-06f sharp aquos pad"], [1920, 1128, 12, "sony xperia z2 tablet (sgp5xx, so-05f, sot21)"], [1920, 1129, 10, "lg g pad ii (v935, v940 prada 3.0, v930 g pad x 10.1, uk932)"], [1920, 1200, 9, "amazon kindle fire hdx 7 (3rd gen, kfthwa, kfthwi)"], [2048, 1440, 8, "hp red"], [2392, 1440, 8, "fujitsu f-02g arrows nx (docomo)"], [2400, 1440, 6, "iuni u3"], [2560, 1440, 7, "vivo x520l xplay 3s"], [2560, 1504, 7, "ntt docomo f-03g (fujitsu arrows tab)"], [2560, 1600, 5, "samsung sm-t525 galaxy tab pro 10.1"]]], ["qualcomm adreno 405", "405", "405 adreno qualcomm", 0, [[1184, 720, 13, "lyf ls-5015 water 8"], [1196, 720, 10, "alcatel 6044 one touch pop up"], [1200, 720, 12, "oppo a53"], [1280, 720, 8, "hisense c1"], [1280, 736, 10, "asus zenpad 8 (adreno 405, p024 z380kl)"], [1776, 1080, 7, "lyf ls-5505"], [1794, 1080, 6, "medion life x5020"], [1798, 1080, 7, "lg h740 g vista 2"], [1800, 1080, 6, "oppo r7s plus"], [1803, 1080, 6, "oppo r7 plus (adreno 405)"], [1812, 1080, 6, "ramos mos1"], [1824, 1080, 6, "vargo ivargo v210101"], [1836, 1080, 6, "lenovo pb1-770m everypad3"], [1920, 1080, 2, "smartisan yq607 jianguo"], [1920, 1104, 5, "lg vk815 g pad x8.3, p815l g pad ii 8.3"], [1920, 1111, 6, "lg g pad x 8.0 (v52x)"], [1794, 1200, 5, "qisda f80"], [1920, 1128, 5, "huawei mediapad t2 10.0 pro (fdr-xxx)"], [1830, 1200, 6, "huawei mediapad x3 (ple-xxx)"], [1836, 1200, 6, "huawei mediapad t2 8.0 pro (jdn-xxx)"]]], ["qualcomm adreno 418", "418", "418 adreno qualcomm", 0, [[1280, 768, 28, "samsung sm-g9198"], [1776, 1080, 17, "sharp sh-m03 aquos mini"], [1794, 1080, 15, "softbank 502sh (sharp aquos xx2)"], [1798, 1080, 17, "sharp aquos serie mini shv33"], [1920, 1080, 13, "qiku q terra (fhd, 8692-a00)"], [2368, 1440, 10, "fujitsu f-02h arrows nx (docomo)"], [2392, 1440, 10, "lg k600"], [2413, 1440, 10, "motorola moto x style, pure (xt1570, xt1572, xt1575)"], [2560, 1440, 9, "qiku q terra (8692-a00)"], [2560, 1504, 9, "ntt docomo f-04h arrows tab"]]], ["qualcomm adreno 420", "420", "420 adreno qualcomm", 0, [[1280, 720, 32, "odg r7-w"], [1920, 1080, 20, "samsung galaxy s5 (adreno 420, sm-g901)"], [2392, 1440, 13, "lg g3 (adreno 420, f460)"], [2560, 1352, 7, "qualcomm apq8084 (adreno 420, development board)"], [2413, 1440, 12, "motorola moto x pro"], [2560, 1440, 8, "qualcomm liquid (adreno 420, windows, development board)"], [2560, 1532, 12, "samsung galaxy note edge (adreno 420, sm-n915x, scl24, sc-01g)"], [2560, 1600, 11, "amazon kindle fire hdx 8.9 (4th gen, k<PERSON><PERSON>, k<PERSON><PERSON>i)"]]], ["qualcomm adreno 430", "430", "430 adreno qualcomm", 0, [[1184, 720, 53, "sony xperia z5 compact (e58xx, so-02h)"], [1280, 720, 18, "zebra technologies mtp8994"], [1776, 1080, 29, "sony xperia z5 (e66xx, so-01h, sov32, 501so)"], [1794, 1080, 19, "vertu signature touch (2015)"], [1920, 1080, 9, "nokia rm-1106"], [1920, 1104, 21, "ntt docomo sh-05g sharp aquos pad"], [2392, 1440, 11, "sony e6508 vzw xperia z4v"], [2413, 1440, 17, "sirin labs solarin"], [2560, 1440, 7, "microsoft rm-1105"], [2560, 1504, 16, "sony xperia z4 tablet (sgp7xx, so-05g, sot31)"]]], ["qualcomm adreno 505", "505", "505 adreno qualcomm", 0, [[728, 480, 29, "zebra technologies tc25"], [1184, 720, 15, "ivvi i3-01"], [1196, 720, 15, "huawei honor 6c (dig-xxx)"], [1199, 720, 14, "lg stylus 2 plus (ms550, k550)"], [1280, 720, 14, "xiaomi land"], [1344, 720, 14, "wiko view prime"], [1368, 720, 13, "micromax hs3"], [1776, 1080, 7, "hisense a2"], [1787, 1080, 7, "lg qua phone px (lgv33)"], [1794, 1080, 7, "pantech im-100 vega"], [1798, 1080, 8, "lg stylo 3 plus (tp450, mp450, m470)"], [1802, 1080, 7, "lg k11 (k530, k535)"], [1812, 1080, 7, "sugar f11"], [1920, 1080, 6, "lg x venture (h700, m710)"], [1920, 1111, 6, "lg g pad x ii 8.0 plus (v530)"], [2004, 1080, 7, "lg q6 (m700, x600)"], [1920, 1128, 7, "vodafone vfd 1400"], [1920, 1132, 7, "huawei mediapad t3 lite 10 (bah-xxx)"], [1839, 1200, 7, "huawei mediapad m3 lite 8.0 (cpn-xxx)"]]], ["qualcomm adreno 506", "506", "506 adreno qualcomm", 0, [[1184, 720, 20, "fujitsu f-04j docomo"], [1280, 720, 20, "samsung galaxy j7 2017 (adreno 506, sm-j727x)"], [1320, 720, 18, "xiaomi <PERSON> 5"], [1356, 720, 18, "vivo v7 plus (1716)"], [1620, 1080, 11, "blackberry bbb100-x (keyone, mercury)"], [1776, 1080, 10, "condor plume h1"], [1788, 1080, 10, "huawei nova (caz-xxx)"], [1794, 1080, 10, "nuans neo reloaded"], [1920, 1080, 9, "blackberry bbd100"], [1920, 1104, 9, "nec lavietab pc-ts508fam"], [1980, 1080, 10, "xiaomi <PERSON><PERSON> 5 plus"], [1920, 1128, 9, "zte k92 primetime"], [1920, 1132, 9, "vestel v tab 1090 lte"], [2094, 1080, 9, "samsung galaxy a6+ (sm-a605fn)"], [1920, 1200, 9, "i.safe is910.1"]]], ["qualcomm adreno 508", "508", "508 adreno qualcomm", 0, [[1776, 1080, 15, "sonim xp8812"], [1798, 1080, 15, "sharp fs8010"], [1920, 1080, 14, "htc u11 life"], [2009, 1080, 14, "vestel venus z20"]]], ["qualcomm adreno 509", "509", "509 adreno qualcomm", 0, [[2062, 1080, 15, "xia<PERSON> <PERSON>mi note 6 pro"], [2159, 1080, 15, "asus zenfone 5"]]], ["qualcomm adreno 510", "510", "510 adreno qualcomm", 0, [[1184, 720, 31, "zebra technologies tc51"], [1208, 800, 27, "askey turbonet tn800a1 turbotab e1"], [1366, 720, 29, "sony xperia touch (g1109)"], [1776, 1080, 13, "agm x2"], [1794, 1080, 14, "qualcomm msm8952 (adreno 510, development board)"], [1920, 1080, 14, "coolpad r116 cool1"], [2048, 1440, 11, "asus zenpad 3 8.0 (p008 z581kl)"], [2160, 1440, 10, "jide remix pro"], [2368, 1440, 10, "vodafone vfd 900"], [2392, 1440, 10, "sharp aquos z3 (fs8009)"], [2413, 1440, 9, "infocus m820"], [2560, 1440, 9, "vivo pd1522a"], [2560, 1504, 9, "lenovo yoga tab3 plus (yt-x703)"]]], ["qualcomm adreno 512", "512", "512 adreno qualcomm", 0, [[782, 480, 59, "honeywell cn80"], [1798, 1080, 23, "sharp fs8016"], [1920, 1080, 22, "vivo td1608"], [2016, 1080, 21, "micromax a200 canvas turbo mini"], [2034, 1080, 22, "vivo x20a"], [2131, 1080, 21, "xia<PERSON> <PERSON>mi note 7"]]], ["qualcomm adreno 530", "530", "530 adreno qualcomm", 0, [[1600, 900, 56, "keecker keecker"], [1776, 1080, 43, "softbank 506sh (sharp aquos phone xx3)"], [1794, 1080, 49, "google pixel"], [1920, 1024, 47, "contextmedia p-wal-107-elc-03"], [1920, 1080, 26, "nokia 6071w"], [2048, 1536, 35, "samsung galaxy tab s3 (sm-t82x)"], [2368, 1440, 27, "motorola xt1650 (1.8 ghz)"], [2392, 1440, 21, "lg q8 (h970, x800l)"], [2416, 1440, 31, "alcatel 6076s"], [2560, 1439, 17, "hp elite x3"], [2560, 1440, 4, "hp falcon"], [2672, 1440, 30, "lg g6 (g600, us997, ls993, vs988, h87x)"], [2880, 1440, 26, "baofeng ke-01"], [3840, 2076, 15, "via vt6093"]]], ["qualcomm adreno 540", "540", "540 adreno qualcomm", 0, [[1184, 720, 61, "sony xperia xz1 compact (g8441)"], [1776, 1080, 57, "sony xperia xz1 (g834x, sov36, so-01k, 701so)"], [1794, 1080, 57, "google pixel 2 (walleye)"], [1920, 1080, 56, "zte nx595j nubia"], [1980, 1080, 56, "xiaomi mi mix 2"], [2034, 1080, 55, "oneplus 5t (a5010)"], [2276, 1312, 43, "essential ph-1"], [2368, 1440, 39, "sharp aquos r (sh-03j, shv39, 605sh)"], [2392, 1440, 41, "qualcomm adreno 540 (development board)"], [2416, 1440, 40, "razer phone"], [2560, 1440, 36, "htc u11 (u-3x, 2pzc100, 2pzc5, htv33, 601ht)"], [2678, 1440, 35, "samsung galaxy s8 active (sm-g892)"], [2768, 1440, 33, "samsung galaxy s8+ (adreno 540, sm-g955x, sc-03j, scv35)"], [2960, 1440, 33, "samsung galaxy note 8 (adreno 540, sm-n950, sc-01k, scv37)"]]], ["qualcomm adreno 610", "610", "610 adreno qualcomm", 0, [[1557, 678, 33, "motorola moto g30"]]], ["qualcomm adreno 612", "612", "612 adreno qualcomm", 0, [[2198, 1080, 20, "samsung galaxy a70 sm-a705fn"]]], ["qualcomm adreno 615", "615", "615 adreno qualcomm", 0, [[2088, 1080, 28, "google pixel 3a xl"], [2560, 1492, 18, "samsung galaxy tab s5e sm-t720"]]], ["qualcomm adreno 616", "616", "616 adreno qualcomm", 0, [[2047, 1080, 31, "meizu x8"], [2135, 1080, 30, "xiaomi mi 9 lite"]]], ["qualcomm adreno 618", "618", "618 adreno qualcomm", 0, [[2138, 1080, 38, "xiaomi mi note 10"], [2304, 1006, 38, "motorola moto g9 plus"], [2168, 1080, 38, "xia<PERSON> <PERSON>mi note 9 pro"], [2179, 1080, 40, "xiaomi poco x3 nfc"], [2183, 1080, 38, "samsung galaxy a71 (sm-a715f)"], [2210, 1080, 35, "xiaomi mi 9t"], [2314, 1044, 31, "samsung galaxy a72 (sm-a725m)"], [2274, 1080, 37, "samsung galaxy a80 sm-a805f"]]], ["qualcomm adreno 619", "619", "619 adreno qualcomm", 0, [[2150, 1080, 44, "oppo reno7 z 5g"]]], ["qualcomm adreno 620", "620", "620 adreno qualcomm", 0, [[2264, 1017, 49, "google pixel 5a 5g"]]], ["qualcomm adreno 630", "630", "630 adreno qualcomm", 0, [[2260, 1035, 58, "oneplus 6t (a6010)"], [2340, 1080, 51, "ritmix rmd-1030"], [2768, 1440, 49, "samsung galaxy s9 (sm-g960u)"], [2792, 1440, 49, "samsung galaxy s9+ (adreno 630, sm-g965)"]]], ["qualcomm adreno 640", "640", "640 adreno qualcomm", 0, [[2020, 1080, 60, "samsung galaxy s10e (adreno 640, sm-g970x)"], [2064, 1080, 60, "samsung galaxy note 10 (adreno 640, sm-n970x)"], [2280, 1014, 85, "google pixel 4"], [2144, 1080, 58, "lg g8 thinq"], [2181, 1080, 60, "samsung galaxy s10 lite"], [2198, 1080, 60, "samsung galaxy a90 5g"], [2292, 1039, 60, "realme x3 superzoom (rmx2086)"], [2210, 1080, 79, "xiaomi redmi k20 pro premium edition"], [2214, 1080, 60, "asus rog phone ii (i001de zs660kl)"], [2048, 1410, 59, "samsung galaxy fold 5g (adreno 640, sm-f900x)"], [2723, 1440, 55, "samsung galaxy s10 (adreno 640, sm-g973x)"], [2730, 1440, 56, "samsung galaxy s10+ (adreno 640, sm-g975x)"], [2733, 1440, 44, "samsung galaxy s10 5g (adreno 640, sm-g977x)"], [2759, 1440, 56, "samsung galaxy note 10+ (adreno 640, sm-n975x)"], [2560, 1564, 60, "xiaomi pad 5 (21051182g)"], [2927, 1440, 53, "samsung galaxy note 10+ 5g (adreno 640, sm-n976x)"]]], ["qualcomm adreno 642l", "642", "642l adreno qualcomm", 0, [[2166, 1080, 70, "xiaomi 11 lite 5g ne"], [2177, 1080, 69, "samsung galaxy a52s 5g"]]], ["qualcomm adreno 644", "644", "644 adreno qualcomm", 0, [[2161, 1080, 60, "oppo reno8 pro (pgam10)"]]], ["qualcomm adreno 650", "650", "650 adreno qualcomm", 0, [[2168, 1080, 108, "samsung galaxy s20 fe 5g (sm-g781u)"], [2178, 1080, 108, "samsung galaxy s20 5g (sm-g981n)"], [2196, 1080, 116, "lenovo l79031"], [2214, 1080, 117, "asus rog phone 3"], [2250, 1080, 116, "xiaomi m2007j1sc"], [2274, 1080, 90, "asus zenfone 7"], [2400, 1080, 60, "xiaomi poco f2 pro"], [2560, 1600, 80, "samsung galaxy tab s7"], [2933, 1440, 57, "samsung galaxy s20 ultra 5g (sm-g988u1)"], [2935, 1440, 57, "samsung galaxy s20+ 5g (sm-g986u)"], [3103, 1440, 50, "samsung galaxy s20+ 5g (sm-g986n)"]]], ["qualcomm adreno 660", "660", "660 adreno qualcomm", 0, [[2190, 1080, 97, "samsung galaxy s21 ultra 5g (sm-g998u1)"], [2293, 1032, 119, "realme gt 5g"], [3063, 1440, 98, "xiaomi m11 (m2011k2c)"]]], ["qualcomm adreno 702", "702", "702 adreno qualcomm", 0, [[1160, 720, 18, "sony nw-a300series"]]], ["qualcomm adreno 720", "720", "720 adreno qualcomm", 0, [[2196, 1080, 109, "htc u24 pro"]]], ["qualcomm adreno 730", "730", "730 adreno qualcomm", 0, [[2106, 1080, 120, "samsung galaxy s22 (sm-s908u)"], [2115, 1080, 120, "samsung galaxy s22 5g (sm-s901u)"], [2183, 1080, 121, "xiaomi 12"], [2184, 1080, 60, "oneplus 10 pro 5g"], [2400, 1080, 144, "nubia red magic 7 (nx679j)"], [2434, 1096, 60, "sony xperia 1 iv"], [2988, 1384, 89, "samsung galaxy s22 ultra (sm-s908e)"], [2800, 1650, 104, "samsung galaxy tab s8+ (sm-x800)"]]], ["qualcomm adreno 740", "740", "740 adreno qualcomm", 0, [[2131, 1080, 120, "samsung galaxy s23+"], [2296, 1080, 121, "xiaomi 13"]]], ["qualcomm adreno 750", "750", "750 adreno qualcomm", 0, [[2109, 1080, 120, "samsung galaxy s24"], [2129, 1080, 120, "samsung galaxy s24 ultra"], [2290, 1038, 120, "xiaomi 14 pro"], [2554, 1152, 121, "xiaomi 14"]]]]