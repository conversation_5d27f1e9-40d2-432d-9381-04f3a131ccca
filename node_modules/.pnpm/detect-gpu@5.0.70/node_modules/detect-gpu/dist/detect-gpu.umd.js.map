{"version": 3, "file": "detect-gpu.umd.js", "sources": ["../src/internal/blocklistedGPUS.ts", "../src/internal/cleanRenderer.ts", "../node_modules/webgl-constants/dist/webgl-constants.esm.js", "../src/internal/ssr.ts", "../src/internal/deviceInfo.ts", "../src/internal/deobfuscateAppleGPU.ts", "../src/internal/error.ts", "../src/internal/getLevenshteinDistance.ts", "../src/internal/util.ts", "../src/index.ts", "../src/internal/getGPUVersion.ts", "../src/internal/getWebGLContext.ts", "../src/internal/deobfuscateRenderer.ts"], "sourcesContent": ["// GPU blocklist\n// SEE: https://chromium.googlesource.com/chromium/src/+/master/gpu/config/software_rendering_list.json\n// SEE: https://hg.mozilla.org/mozilla-central/raw-file/tip/services/settings/dumps/blocklists/gfx.json\nexport const BLOCKLISTED_GPUS = [\n  'geforce 320m',\n  'geforce 8600',\n  'geforce 8600m gt',\n  'geforce 8800 gs',\n  'geforce 8800 gt',\n  'geforce 9400',\n  'geforce 9400m g',\n  'geforce 9400m',\n  'geforce 9600m gt',\n  'geforce 9600m',\n  'geforce fx go5200',\n  'geforce gt 120',\n  'geforce gt 130',\n  'geforce gt 330m',\n  'geforce gtx 285',\n  'google swiftshader',\n  'intel g41',\n  'intel g45',\n  'intel gma 4500mhd',\n  'intel gma x3100',\n  'intel hd 3000',\n  'intel q45',\n  'legacy',\n  'mali-2',\n  'mali-3',\n  'mali-4',\n  'quadro fx 1500',\n  'quadro fx 4',\n  'quadro fx 5',\n  'radeon hd 2400',\n  'radeon hd 2600',\n  'radeon hd 4670',\n  'radeon hd 4850',\n  'radeon hd 4870',\n  'radeon hd 5670',\n  'radeon hd 5750',\n  'radeon hd 6290',\n  'radeon hd 6300',\n  'radeon hd 6310',\n  'radeon hd 6320',\n  'radeon hd 6490m',\n  'radeon hd 6630m',\n  'radeon hd 6750m',\n  'radeon hd 6770m',\n  'radeon hd 6970m',\n  'sgx 543',\n  'sgx543',\n];\n", "const debug = false ? console.log : undefined;\n\nexport function cleanRenderer(renderer: string) {\n  debug?.('cleanRenderer', { renderer });\n\n  renderer = renderer\n    .toLowerCase()\n    // Strip off ANGLE() - for example:\n    // 'ANGLE (NVIDIA TITAN Xp)' becomes 'NVIDIA TITAN Xp',\n    // 'Samsung Electronics Co., Ltd. ANGLE (Samsung Xclipse 920) on Vulkan 1.1.179' becomes 'Samsung Xclipse 920':\n    .replace(/.*angle ?\\((.+)\\)(?: on vulkan [0-9.]+)?$/i, '$1')\n    // Strip off [number]gb & strip off direct3d and after - for example:\n    // 'Radeon (TM) RX 470 Series Direct3D11 vs_5_0 ps_5_0' becomes\n    // 'Radeon (TM) RX 470 Series'\n    .replace(/\\s(\\d{1,2}gb|direct3d.+$)|\\(r\\)| \\([^)]+\\)$/g, '')\n    // Strip out graphics API. The one Vulkan example we've seen includes\n    // the GPU in parens after the Vulkan version so this also keeps that\n    // eg. 'vulkan 1.2.175 (nvidia nvidia geforce gtx 970 (0x000013c2))'\n    // becomes 'nvidia nvidia geforce gtx 970 (0x000013c2)'\n    // `OpenGL 4.5.0` gets removed all together\n    .replace(/(?:vulkan|opengl) \\d+\\.\\d+(?:\\.\\d+)?(?: \\((.*)\\))?/, '$1')\n\n  debug?.('cleanRenderer - renderer cleaned to', { renderer });\n\n  return renderer;\n};\n", "/**\r\n * The following defined constants and descriptions are directly ported from https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/Constants\r\n *\r\n * Any copyright is dedicated to the Public Domain. http://creativecommons.org/publicdomain/zero/1.0/\r\n *\r\n * Contributors\r\n *\r\n * See: https://developer.mozilla.org/en-US/profiles/Sheppy\r\n * See: https://developer.mozilla.org/en-US/profiles/fscholz\r\n * See: https://developer.mozilla.org/en-US/profiles/AtiX\r\n * See: https://developer.mozilla.org/en-US/profiles/Sebastianz\r\n *\r\n * These constants are defined on the WebGLRenderingContext / WebGL2RenderingContext interface\r\n */\r\n// Clearing buffers\r\n// Constants passed to WebGLRenderingContext.clear() to clear buffer masks\r\n/**\r\n * Passed to clear to clear the current depth buffer\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_BUFFER_BIT = 0x00000100;\r\n/**\r\n * Passed to clear to clear the current stencil buffer\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BUFFER_BIT = 0x00000400;\r\n/**\r\n * Passed to clear to clear the current color buffer\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_BUFFER_BIT = 0x00004000;\r\n// Rendering primitives\r\n// Constants passed to WebGLRenderingContext.drawElements() or WebGLRenderingContext.drawArrays() to specify what kind of primitive to render\r\n/**\r\n * Passed to drawElements or drawArrays to draw single points\r\n * @constant {number}\r\n */\r\nconst GL_POINTS = 0x0000;\r\n/**\r\n * Passed to drawElements or drawArrays to draw lines. Each vertex connects to the one after it\r\n * @constant {number}\r\n */\r\nconst GL_LINES = 0x0001;\r\n/**\r\n * Passed to drawElements or drawArrays to draw lines. Each set of two vertices is treated as a separate line segment\r\n * @constant {number}\r\n */\r\nconst GL_LINE_LOOP = 0x0002;\r\n/**\r\n * Passed to drawElements or drawArrays to draw a connected group of line segments from the first vertex to the last\r\n * @constant {number}\r\n */\r\nconst GL_LINE_STRIP = 0x0003;\r\n/**\r\n * Passed to drawElements or drawArrays to draw triangles. Each set of three vertices creates a separate triangle\r\n * @constant {number}\r\n */\r\nconst GL_TRIANGLES = 0x0004;\r\n/**\r\n * Passed to drawElements or drawArrays to draw a connected group of triangles\r\n * @constant {number}\r\n */\r\nconst GL_TRIANGLE_STRIP = 0x0005;\r\n/**\r\n * Passed to drawElements or drawArrays to draw a connected group of triangles. Each vertex connects to the previous and the first vertex in the fan\r\n * @constant {number}\r\n */\r\nconst GL_TRIANGLE_FAN = 0x0006;\r\n// Blending modes\r\n// Constants passed to WebGLRenderingContext.blendFunc() or WebGLRenderingContext.blendFuncSeparate() to specify the blending mode (for both, RBG and alpha, or separately)\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to turn off a component\r\n * @constant {number}\r\n */\r\nconst GL_ZERO = 0;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to turn on a component\r\n * @constant {number}\r\n */\r\nconst GL_ONE = 1;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by the source elements color\r\n * @constant {number}\r\n */\r\nconst GL_SRC_COLOR = 0x0300;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by one minus the source elements color\r\n * @constant {number}\r\n */\r\nconst GL_ONE_MINUS_SRC_COLOR = 0x0301;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by the source's alpha\r\n * @constant {number}\r\n */\r\nconst GL_SRC_ALPHA = 0x0302;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by one minus the source's alpha\r\n * @constant {number}\r\n */\r\nconst GL_ONE_MINUS_SRC_ALPHA = 0x0303;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by the destination's alpha\r\n * @constant {number}\r\n */\r\nconst GL_DST_ALPHA = 0x0304;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by one minus the destination's alpha\r\n * @constant {number}\r\n */\r\nconst GL_ONE_MINUS_DST_ALPHA = 0x0305;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by the destination's color\r\n * @constant {number}\r\n */\r\nconst GL_DST_COLOR = 0x0306;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by one minus the destination's color\r\n * @constant {number}\r\n */\r\nconst GL_ONE_MINUS_DST_COLOR = 0x0307;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by the minimum of source's alpha or one minus the destination's alpha\r\n * @constant {number}\r\n */\r\nconst GL_SRC_ALPHA_SATURATE = 0x0308;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to specify a constant color blend function\r\n * @constant {number}\r\n */\r\nconst GL_CONSTANT_COLOR = 0x8001;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to specify one minus a constant color blend function\r\n * @constant {number}\r\n */\r\nconst GL_ONE_MINUS_CONSTANT_COLOR = 0x8002;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to specify a constant alpha blend function\r\n * @constant {number}\r\n */\r\nconst GL_CONSTANT_ALPHA = 0x8003;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to specify one minus a constant alpha blend function\r\n * @constant {number}\r\n */\r\nconst GL_ONE_MINUS_CONSTANT_ALPHA = 0x8004;\r\n// Blending equations\r\n// Constants passed to WebGLRenderingContext.blendEquation() or WebGLRenderingContext.blendEquationSeparate() to control how the blending is calculated (for both, RBG and alpha, or separately)\r\n/**\r\n * Passed to blendEquation or blendEquationSeparate to set an addition blend function\r\n * @constant {number}\r\n */\r\nconst GL_FUNC_ADD = 0x8006;\r\n/**\r\n * Passed to blendEquation or blendEquationSeparate to specify a subtraction blend function (source - destination)\r\n * @constant {number}\r\n */\r\nconst GL_FUNC_SUBSTRACT = 0x800a;\r\n/**\r\n * Passed to blendEquation or blendEquationSeparate to specify a reverse subtraction blend function (destination - source)\r\n * @constant {number}\r\n */\r\nconst GL_FUNC_REVERSE_SUBTRACT = 0x800b;\r\n// Getting GL parameter information\r\n// Constants passed to WebGLRenderingContext.getParameter() to specify what information to return\r\n/**\r\n * Passed to getParameter to get the current RGB blend function\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_EQUATION = 0x8009;\r\n/**\r\n * Passed to getParameter to get the current RGB blend function. Same as BLEND_EQUATION\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_EQUATION_RGB = 0x8009;\r\n/**\r\n * Passed to getParameter to get the current alpha blend function. Same as BLEND_EQUATION\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_EQUATION_ALPHA = 0x883d;\r\n/**\r\n * Passed to getParameter to get the current destination RGB blend function\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_DST_RGB = 0x80c8;\r\n/**\r\n * Passed to getParameter to get the current source RGB blend function\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_SRC_RGB = 0x80c9;\r\n/**\r\n * Passed to getParameter to get the current destination alpha blend function\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_DST_ALPHA = 0x80ca;\r\n/**\r\n * Passed to getParameter to get the current source alpha blend function\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_SRC_ALPHA = 0x80cb;\r\n/**\r\n * Passed to getParameter to return a the current blend color\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_COLOR = 0x8005;\r\n/**\r\n * Passed to getParameter to get the array buffer binding\r\n * @constant {number}\r\n */\r\nconst GL_ARRAY_BUFFER_BINDING = 0x8894;\r\n/**\r\n * Passed to getParameter to get the current element array buffer\r\n * @constant {number}\r\n */\r\nconst GL_ELEMENT_ARRAY_BUFFER_BINDING = 0x8895;\r\n/**\r\n * Passed to getParameter to get the current lineWidth (set by the lineWidth method)\r\n * @constant {number}\r\n */\r\nconst GL_LINE_WIDTH = 0x0b21;\r\n/**\r\n * Passed to getParameter to get the current size of a point drawn with gl.POINTS\r\n * @constant {number}\r\n */\r\nconst GL_ALIASED_POINT_SIZE_RANGE = 0x846d;\r\n/**\r\n * Passed to getParameter to get the range of available widths for a line. Returns a length-2 array with the lo value at 0, and hight at 1\r\n * @constant {number}\r\n */\r\nconst GL_ALIASED_LINE_WIDTH_RANGE = 0x846e;\r\n/**\r\n * Passed to getParameter to get the current value of cullFace. Should return FRONT, BACK, or FRONT_AND_BACK\r\n * @constant {number}\r\n */\r\nconst GL_CULL_FACE_MODE = 0x0b45;\r\n/**\r\n * Passed to getParameter to determine the current value of frontFace. Should return CW or CCW\r\n * @constant {number}\r\n */\r\nconst GL_FRONT_FACE = 0x0b46;\r\n/**\r\n * Passed to getParameter to return a length-2 array of floats giving the current depth range\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_RANGE = 0x0b70;\r\n/**\r\n * Passed to getParameter to determine if the depth write mask is enabled\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_WRITEMASK = 0x0b72;\r\n/**\r\n * Passed to getParameter to determine the current depth clear value\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_CLEAR_VALUE = 0x0b73;\r\n/**\r\n * Passed to getParameter to get the current depth function. Returns NEVER, ALWAYS, LESS, EQUAL, LEQUAL, GREATER, GEQUAL, or NOTEQUAL\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_FUNC = 0x0b74;\r\n/**\r\n * Passed to getParameter to get the value the stencil will be cleared to\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_CLEAR_VALUE = 0x0b91;\r\n/**\r\n * Passed to getParameter to get the current stencil function. Returns NEVER, ALWAYS, LESS, EQUAL, LEQUAL, GREATER, GEQUAL, or NOTEQUAL\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_FUNC = 0x0b92;\r\n/**\r\n * Passed to getParameter to get the current stencil fail function. Should return KEEP, REPLACE, INCR, DECR, INVERT, INCR_WRAP, or DECR_WRAP\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_FAIL = 0x0b94;\r\n/**\r\n * Passed to getParameter to get the current stencil fail function should the depth buffer test fail. Should return KEEP, REPLACE, INCR, DECR, INVERT, INCR_WRAP, or DECR_WRAP\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_PASS_DEPTH_FAIL = 0x0b95;\r\n/**\r\n * Passed to getParameter to get the current stencil fail function should the depth buffer test pass. Should return KEEP, REPLACE, INCR, DECR, INVERT, INCR_WRAP, or DECR_WRAP\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_PASS_DEPTH_PASS = 0x0b96;\r\n/**\r\n * Passed to getParameter to get the reference value used for stencil tests\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_REF = 0x0b97;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_VALUE_MASK = 0x0b93;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_WRITEMASK = 0x0b98;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_FUNC = 0x8800;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_FAIL = 0x8801;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_PASS_DEPTH_FAIL = 0x8802;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_PASS_DEPTH_PASS = 0x8803;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_REF = 0x8ca3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_VALUE_MASK = 0x8ca4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_WRITEMASK = 0x8ca5;\r\n/**\r\n * Returns an Int32Array with four elements for the current viewport dimensions\r\n * @constant {number}\r\n */\r\nconst GL_VIEWPORT = 0x0ba2;\r\n/**\r\n * Returns an Int32Array with four elements for the current scissor box dimensions\r\n * @constant {number}\r\n */\r\nconst GL_SCISSOR_BOX = 0x0c10;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_CLEAR_VALUE = 0x0c22;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_WRITEMASK = 0x0c23;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_ALIGNMENT = 0x0cf5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PACK_ALIGNMENT = 0x0d05;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TEXTURE_SIZE = 0x0d33;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VIEWPORT_DIMS = 0x0d3a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SUBPIXEL_BITS = 0x0d50;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RED_BITS = 0x0d52;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_GREEN_BITS = 0x0d53;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BLUE_BITS = 0x0d54;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_ALPHA_BITS = 0x0d55;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_BITS = 0x0d56;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BITS = 0x0d57;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_POLYGON_OFFSET_UNITS = 0x2a00;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_POLYGON_OFFSET_FACTOR = 0x8038;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_BINDING_2D = 0x8069;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLE_BUFFERS = 0x80a8;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLES = 0x80a9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLE_COVERAGE_VALUE = 0x80aa;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLE_COVERAGE_INVERT = 0x80ab;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_TEXTURE_FORMATS = 0x86a3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VENDOR = 0x1f00;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERER = 0x1f01;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERSION = 0x1f02;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_IMPLEMENTATION_COLOR_READ_TYPE = 0x8b9a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_IMPLEMENTATION_COLOR_READ_FORMAT = 0x8b9b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BROWSER_DEFAULT_WEBGL = 0x9244;\r\n// Buffers\r\n// Constants passed to WebGLRenderingContext.bufferData(), WebGLRenderingContext.bufferSubData(), WebGLRenderingContext.bindBuffer(), or WebGLRenderingContext.getBufferParameter()\r\n/**\r\n * Passed to bufferData as a hint about whether the contents of the buffer are likely to be used often and not change often\r\n * @constant {number}\r\n */\r\nconst GL_STATIC_DRAW = 0x88e4;\r\n/**\r\n * Passed to bufferData as a hint about whether the contents of the buffer are likely to not be used often\r\n * @constant {number}\r\n */\r\nconst GL_STREAM_DRAW = 0x88e0;\r\n/**\r\n * Passed to bufferData as a hint about whether the contents of the buffer are likely to be used often and change often\r\n * @constant {number}\r\n */\r\nconst GL_DYNAMIC_DRAW = 0x88e8;\r\n/**\r\n * Passed to bindBuffer or bufferData to specify the type of buffer being used\r\n * @constant {number}\r\n */\r\nconst GL_ARRAY_BUFFER = 0x8892;\r\n/**\r\n * Passed to bindBuffer or bufferData to specify the type of buffer being used\r\n * @constant {number}\r\n */\r\nconst GL_ELEMENT_ARRAY_BUFFER = 0x8893;\r\n/**\r\n * Passed to getBufferParameter to get a buffer's size\r\n * @constant {number}\r\n */\r\nconst GL_BUFFER_SIZE = 0x8764;\r\n/**\r\n * Passed to getBufferParameter to get the hint for the buffer passed in when it was created\r\n * @constant {number}\r\n */\r\nconst GL_BUFFER_USAGE = 0x8765;\r\n// Vertex attributes\r\n// Constants passed to WebGLRenderingContext.getVertexAttrib()\r\n/**\r\n * Passed to getVertexAttrib to read back the current vertex attribute\r\n * @constant {number}\r\n */\r\nconst GL_CURRENT_VERTEX_ATTRIB = 0x8626;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_ENABLED = 0x8622;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_SIZE = 0x8623;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_STRIDE = 0x8624;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_TYPE = 0x8625;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_NORMALIZED = 0x886a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_POINTER = 0x8645;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_BUFFER_BINDING = 0x889f;\r\n// Culling\r\n// Constants passed to WebGLRenderingContext.cullFace()\r\n/**\r\n * Passed to enable/disable to turn on/off culling. Can also be used with getParameter to find the current culling method\r\n * @constant {number}\r\n */\r\nconst GL_CULL_FACE = 0x0b44;\r\n/**\r\n * Passed to cullFace to specify that only front faces should be culled\r\n * @constant {number}\r\n */\r\nconst GL_FRONT = 0x0404;\r\n/**\r\n * Passed to cullFace to specify that only back faces should be culled\r\n * @constant {number}\r\n */\r\nconst GL_BACK = 0x0405;\r\n/**\r\n * Passed to cullFace to specify that front and back faces should be culled\r\n * @constant {number}\r\n */\r\nconst GL_FRONT_AND_BACK = 0x0408;\r\n// Enabling and disabling\r\n// Constants passed to WebGLRenderingContext.enable() or WebGLRenderingContext.disable()\r\n/**\r\n * Passed to enable/disable to turn on/off blending. Can also be used with getParameter to find the current blending method\r\n * @constant {number}\r\n */\r\nconst GL_BLEND = 0x0be2;\r\n/**\r\n * Passed to enable/disable to turn on/off the depth test. Can also be used with getParameter to query the depth test\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_TEST = 0x0b71;\r\n/**\r\n * Passed to enable/disable to turn on/off dithering. Can also be used with getParameter to find the current dithering method\r\n * @constant {number}\r\n */\r\nconst GL_DITHER = 0x0bd0;\r\n/**\r\n * Passed to enable/disable to turn on/off the polygon offset. Useful for rendering hidden-line images, decals, and or solids with highlighted edges. Can also be used with getParameter to query the scissor test\r\n * @constant {number}\r\n */\r\nconst GL_POLYGON_OFFSET_FILL = 0x8037;\r\n/**\r\n * Passed to enable/disable to turn on/off the alpha to coverage. Used in multi-sampling alpha channels\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLE_ALPHA_TO_COVERAGE = 0x809e;\r\n/**\r\n * Passed to enable/disable to turn on/off the sample coverage. Used in multi-sampling\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLE_COVERAGE = 0x80a0;\r\n/**\r\n * Passed to enable/disable to turn on/off the scissor test. Can also be used with getParameter to query the scissor test\r\n * @constant {number}\r\n */\r\nconst GL_SCISSOR_TEST = 0x0c11;\r\n/**\r\n * Passed to enable/disable to turn on/off the stencil test. Can also be used with getParameter to query the stencil test\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_TEST = 0x0b90;\r\n// Errors\r\n// Constants returned from WebGLRenderingContext.getError()\r\n/**\r\n * Returned from getError\r\n * @constant {number}\r\n */\r\nconst GL_NO_ERROR = 0;\r\n/**\r\n * Returned from getError\r\n * @constant {number}\r\n */\r\nconst GL_INVALID_ENUM = 0x0500;\r\n/**\r\n * Returned from getError\r\n * @constant {number}\r\n */\r\nconst GL_INVALID_VALUE = 0x0501;\r\n/**\r\n * Returned from getError\r\n * @constant {number}\r\n */\r\nconst GL_INVALID_OPERATION = 0x0502;\r\n/**\r\n * Returned from getError\r\n * @constant {number}\r\n */\r\nconst GL_OUT_OF_MEMORY = 0x0505;\r\n/**\r\n * Returned from getError\r\n * @constant {number}\r\n */\r\nconst GL_CONTEXT_LOST_WEBGL = 0x9242;\r\n// Front face directions\r\n// Constants passed to WebGLRenderingContext.frontFace()\r\n/**\r\n * Passed to frontFace to specify the front face of a polygon is drawn in the clockwise direction,\r\n * @constant {number}\r\n */\r\nconst GL_CW = 0x0900;\r\n/**\r\n * Passed to frontFace to specify the front face of a polygon is drawn in the counter clockwise direction\r\n * @constant {number}\r\n */\r\nconst GL_CCW = 0x0901;\r\n// Hints\r\n// Constants passed to WebGLRenderingContext.hint()\r\n/**\r\n * There is no preference for this behavior\r\n * @constant {number}\r\n */\r\nconst GL_DONT_CARE = 0x1100;\r\n/**\r\n * The most efficient behavior should be used\r\n * @constant {number}\r\n */\r\nconst GL_FASTEST = 0x1101;\r\n/**\r\n * The most correct or the highest quality option should be used\r\n * @constant {number}\r\n */\r\nconst GL_NICEST = 0x1102;\r\n/**\r\n * Hint for the quality of filtering when generating mipmap images with WebGLRenderingContext.generateMipmap()\r\n * @constant {number}\r\n */\r\nconst GL_GENERATE_MIPMAP_HINT = 0x8192;\r\n// Data types\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BYTE = 0x1400;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_BYTE = 0x1401;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SHORT = 0x1402;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_SHORT = 0x1403;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT = 0x1404;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT = 0x1405;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT = 0x1406;\r\n// Pixel formats\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_COMPONENT = 0x1902;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_ALPHA = 0x1906;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB = 0x1907;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA = 0x1908;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LUMINANCE = 0x1909;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LUMINANCE_ALPHA = 0x190a;\r\n// Pixel types\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_SHORT_4_4_4_4 = 0x8033;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_SHORT_5_5_5_1 = 0x8034;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_SHORT_5_6_5 = 0x8363;\r\n// Shaders\r\n// Constants passed to WebGLRenderingContext.getShaderParameter()\r\n/**\r\n * Passed to createShader to define a fragment shader\r\n * @constant {number}\r\n */\r\nconst GL_FRAGMENT_SHADER = 0x8b30;\r\n/**\r\n * Passed to createShader to define a vertex shader\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_SHADER = 0x8b31;\r\n/**\r\n * Passed to getShaderParamter to get the status of the compilation. Returns false if the shader was not compiled. You can then query getShaderInfoLog to find the exact error\r\n * @constant {number}\r\n */\r\nconst GL_COMPILE_STATUS = 0x8b81;\r\n/**\r\n * Passed to getShaderParamter to determine if a shader was deleted via deleteShader. Returns true if it was, false otherwise\r\n * @constant {number}\r\n */\r\nconst GL_DELETE_STATUS = 0x8b80;\r\n/**\r\n * Passed to getProgramParameter after calling linkProgram to determine if a program was linked correctly. Returns false if there were errors. Use getProgramInfoLog to find the exact error\r\n * @constant {number}\r\n */\r\nconst GL_LINK_STATUS = 0x8b82;\r\n/**\r\n * Passed to getProgramParameter after calling validateProgram to determine if it is valid. Returns false if errors were found\r\n * @constant {number}\r\n */\r\nconst GL_VALIDATE_STATUS = 0x8b83;\r\n/**\r\n * Passed to getProgramParameter after calling attachShader to determine if the shader was attached correctly. Returns false if errors occurred\r\n * @constant {number}\r\n */\r\nconst GL_ATTACHED_SHADERS = 0x8b85;\r\n/**\r\n * Passed to getProgramParameter to get the number of attributes active in a program\r\n * @constant {number}\r\n */\r\nconst GL_ACTIVE_ATTRIBUTES = 0x8b89;\r\n/**\r\n * Passed to getProgramParamter to get the number of uniforms active in a program\r\n * @constant {number}\r\n */\r\nconst GL_ACTIVE_UNIFORMS = 0x8b86;\r\n/**\r\n * The maximum number of entries possible in the vertex attribute list\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VERTEX_ATTRIBS = 0x8869;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VERTEX_UNIFORM_VECTORS = 0x8dfb;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VARYING_VECTORS = 0x8dfc;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS = 0x8b4d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS = 0x8b4c;\r\n/**\r\n * Implementation dependent number of maximum texture units. At least 8\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TEXTURE_IMAGE_UNITS = 0x8872;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_FRAGMENT_UNIFORM_VECTORS = 0x8dfd;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SHADER_TYPE = 0x8b4f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SHADING_LANGUAGE_VERSION = 0x8b8c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_CURRENT_PROGRAM = 0x8b8d;\r\n// Depth or stencil tests\r\n// Constants passed to WebGLRenderingContext.stencilFunc()\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will never pass. i.e. Nothing will be drawn\r\n * @constant {number}\r\n */\r\nconst GL_NEVER = 0x0200;\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will always pass. i.e. Pixels will be drawn in the order they are drawn\r\n * @constant {number}\r\n */\r\nconst GL_ALWAYS = 0x0207;\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is less than the stored value\r\n * @constant {number}\r\n */\r\nconst GL_LESS = 0x0201;\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is equals to the stored value\r\n * @constant {number}\r\n */\r\nconst GL_EQUAL = 0x0202;\r\n/**\r\n *  Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is less than or equal to the stored value\r\n * @constant {number}\r\n */\r\nconst GL_LEQUAL = 0x0203;\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is greater than the stored value\r\n * @constant {number}\r\n */\r\nconst GL_GREATER = 0x0204;\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is greater than or equal to the stored value\r\n * @constant {number}\r\n */\r\nconst GL_GEQUAL = 0x0206;\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is not equal to the stored value\r\n * @constant {number}\r\n */\r\nconst GL_NOTEQUAL = 0x0205;\r\n// Stencil actions\r\n// Constants passed to WebGLRenderingContext.stencilOp()\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_KEEP = 0x1e00;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_REPLACE = 0x1e01;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INCR = 0x1e02;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DECR = 0x1e03;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INVERT = 0x150a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INCR_WRAP = 0x8507;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DECR_WRAP = 0x8508;\r\n// Textures\r\n// Constants passed to WebGLRenderingContext.texParameteri(), WebGLRenderingContext.texParameterf(), WebGLRenderingContext.bindTexture(), WebGLRenderingContext.texImage2D(), and others\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_NEAREST = 0x2600;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LINEAR = 0x2601;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_NEAREST_MIPMAP_NEAREST = 0x2700;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LINEAR_MIPMAP_NEAREST = 0x2701;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_NEAREST_MIPMAP_LINEAR = 0x2702;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LINEAR_MIPMAP_LINEAR = 0x2703;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_MAG_FILTER = 0x2800;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_MIN_FILTER = 0x2801;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_WRAP_S = 0x2802;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_WRAP_T = 0x2803;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_2D = 0x0de1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE = 0x1702;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP = 0x8513;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_BINDING_CUBE_MAP = 0x8514;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP_POSITIVE_X = 0x8515;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP_NEGATIVE_X = 0x8516;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP_POSITIVE_Y = 0x8517;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP_NEGATIVE_Y = 0x8518;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP_POSITIVE_Z = 0x8519;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP_NEGATIVE_Z = 0x851a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_CUBE_MAP_TEXTURE_SIZE = 0x851c;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE0 = 0x84c0;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE1 = 0x84c1;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE2 = 0x84c2;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE3 = 0x84c3;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE4 = 0x84c4;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE5 = 0x84c5;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE6 = 0x84c6;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE7 = 0x84c7;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE8 = 0x84c8;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE9 = 0x84c9;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE10 = 0x84ca;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE11 = 0x84cb;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE12 = 0x84cc;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE13 = 0x84cd;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE14 = 0x84ce;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE15 = 0x84cf;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE16 = 0x84d0;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE17 = 0x84d1;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE18 = 0x84d2;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE19 = 0x84d3;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE20 = 0x84d4;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE21 = 0x84d5;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE22 = 0x84d6;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE23 = 0x84d7;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE24 = 0x84d8;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE25 = 0x84d9;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE26 = 0x84da;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE27 = 0x84db;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE28 = 0x84dc;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE29 = 0x84dd;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE30 = 0x84de;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE31 = 0x84df;\r\n/**\r\n * The current active texture unit\r\n * @constant {number}\r\n */\r\nconst GL_ACTIVE_TEXTURE = 0x84e0;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_REPEAT = 0x2901;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_CLAMP_TO_EDGE = 0x812f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MIRRORED_REPEAT = 0x8370;\r\n// Uniform types\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_VEC2 = 0x8b50;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_VEC3 = 0x8b51;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_VEC4 = 0x8b52;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_VEC2 = 0x8b53;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_VEC3 = 0x8b54;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_VEC4 = 0x8b55;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BOOL = 0x8b56;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BOOL_VEC2 = 0x8b57;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BOOL_VEC3 = 0x8b58;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BOOL_VEC4 = 0x8b59;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT2 = 0x8b5a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT3 = 0x8b5b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT4 = 0x8b5c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_2D = 0x8b5e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_CUBE = 0x8b60;\r\n// Shader precision-specified types\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LOW_FLOAT = 0x8df0;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MEDIUM_FLOAT = 0x8df1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_HIGH_FLOAT = 0x8df2;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LOW_INT = 0x8df3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MEDIUM_INT = 0x8df4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_HIGH_INT = 0x8df5;\r\n// Framebuffers and renderbuffers\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER = 0x8d40;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER = 0x8d41;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA4 = 0x8056;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB5_A1 = 0x8057;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB565 = 0x8d62;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_COMPONENT16 = 0x81a5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_INDEX = 0x1901;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_INDEX8 = 0x8d48;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_STENCIL = 0x84f9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_WIDTH = 0x8d42;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_HEIGHT = 0x8d43;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_INTERNAL_FORMAT = 0x8d44;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_RED_SIZE = 0x8d50;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_GREEN_SIZE = 0x8d51;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_BLUE_SIZE = 0x8d52;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_ALPHA_SIZE = 0x8d53;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_DEPTH_SIZE = 0x8d54;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_STENCIL_SIZE = 0x8d55;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE = 0x8cd0;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_OBJECT_NAME = 0x8cd1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL = 0x8cd2;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE = 0x8cd3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT0 = 0x8ce0;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_ATTACHMENT = 0x8d00;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_ATTACHMENT = 0x8d20;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_STENCIL_ATTACHMENT = 0x821a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_NONE = 0;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_COMPLETE = 0x8cd5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT = 0x8cd6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT = 0x8cd7;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_INCOMPLETE_DIMENSIONS = 0x8cd9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_UNSUPPORTED = 0x8cdd;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_BINDING = 0x8ca6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_BINDING = 0x8ca7;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_RENDERBUFFER_SIZE = 0x84e8;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INVALID_FRAMEBUFFER_OPERATION = 0x0506;\r\n// Pixel storage modes\r\n// Constants passed to WebGLRenderingContext.pixelStorei()\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_FLIP_Y_WEBGL = 0x9240;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_PREMULTIPLY_ALPHA_WEBGL = 0x9241;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_COLORSPACE_CONVERSION_WEBGL = 0x9243;\r\n// Additional constants defined WebGL 2\r\n// These constants are defined on the WebGL2RenderingContext interface. All WebGL 1 constants are also available in a WebGL 2 context\r\n// Getting GL parameter information\r\n// Constants passed to WebGLRenderingContext.getParameter() to specify what information to return\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_READ_BUFFER = 0x0c02;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_ROW_LENGTH = 0x0cf2;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_SKIP_ROWS = 0x0cf3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_SKIP_PIXELS = 0x0cf4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PACK_ROW_LENGTH = 0x0d02;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PACK_SKIP_ROWS = 0x0d03;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PACK_SKIP_PIXELS = 0x0d04;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_BINDING_3D = 0x806a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_SKIP_IMAGES = 0x806d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_IMAGE_HEIGHT = 0x806e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_3D_TEXTURE_SIZE = 0x8073;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_ELEMENTS_VERTICES = 0x80e8;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_ELEMENTS_INDICES = 0x80e9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TEXTURE_LOD_BIAS = 0x84fd;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_FRAGMENT_UNIFORM_COMPONENTS = 0x8b49;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VERTEX_UNIFORM_COMPONENTS = 0x8b4a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_ARRAY_TEXTURE_LAYERS = 0x88ff;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MIN_PROGRAM_TEXEL_OFFSET = 0x8904;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_PROGRAM_TEXEL_OFFSET = 0x8905;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VARYING_COMPONENTS = 0x8b4b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAGMENT_SHADER_DERIVATIVE_HINT = 0x8b8b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RASTERIZER_DISCARD = 0x8c89;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ARRAY_BINDING = 0x85b5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VERTEX_OUTPUT_COMPONENTS = 0x9122;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_FRAGMENT_INPUT_COMPONENTS = 0x9125;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_SERVER_WAIT_TIMEOUT = 0x9111;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_ELEMENT_INDEX = 0x8d6b;\r\n// Textures\r\n// Constants passed to WebGLRenderingContext.texParameteri(), WebGLRenderingContext.texParameterf(), WebGLRenderingContext.bindTexture(), WebGLRenderingContext.texImage2D(), and others\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RED = 0x1903;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB8 = 0x8051;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA8 = 0x8058;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB10_A2 = 0x8059;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_3D = 0x806f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_WRAP_R = 0x8072;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_MIN_LOD = 0x813a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_MAX_LOD = 0x813b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_BASE_LEVEL = 0x813c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_MAX_LEVEL = 0x813d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_COMPARE_MODE = 0x884c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_COMPARE_FUNC = 0x884d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SRGB = 0x8c40;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SRGB8 = 0x8c41;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SRGB8_ALPHA8 = 0x8c43;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COMPARE_REF_TO_TEXTURE = 0x884e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA32F = 0x8814;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB32F = 0x8815;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA16F = 0x881a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB16F = 0x881b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_2D_ARRAY = 0x8c1a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_BINDING_2D_ARRAY = 0x8c1d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R11F_G11F_B10F = 0x8c3a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB9_E5 = 0x8c3d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA32UI = 0x8d70;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB32UI = 0x8d71;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA16UI = 0x8d76;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB16UI = 0x8d77;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA8UI = 0x8d7c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB8UI = 0x8d7d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA32I = 0x8d82;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB32I = 0x8d83;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA16I = 0x8d88;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB16I = 0x8d89;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA8I = 0x8d8e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB8I = 0x8d8f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RED_INTEGER = 0x8d94;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB_INTEGER = 0x8d98;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA_INTEGER = 0x8d99;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R8 = 0x8229;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG8 = 0x822b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R16F = 0x822d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R32F = 0x822e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG16F = 0x822f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG32F = 0x8230;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R8I = 0x8231;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R8UI = 0x8232;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R16I = 0x8233;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R16UI = 0x8234;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R32I = 0x8235;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R32UI = 0x8236;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG8I = 0x8237;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG8UI = 0x8238;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG16I = 0x8239;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG16UI = 0x823a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG32I = 0x823b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG32UI = 0x823c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R8_SNORM = 0x8f94;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG8_SNORM = 0x8f95;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB8_SNORM = 0x8f96;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA8_SNORM = 0x8f97;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB10_A2UI = 0x906f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_IMMUTABLE_FORMAT = 0x912f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_IMMUTABLE_LEVELS = 0x82df;\r\n// Pixel types\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_2_10_10_10_REV = 0x8368;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_10F_11F_11F_REV = 0x8c3b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_5_9_9_9_REV = 0x8c3e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_32_UNSIGNED_INT_24_8_REV = 0x8dad;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_24_8 = 0x84fa;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_HALF_FLOAT = 0x140b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG = 0x8227;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG_INTEGER = 0x8228;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_2_10_10_10_REV = 0x8d9f;\r\n// Queries\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_CURRENT_QUERY = 0x8865;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_QUERY_RESULT = 0x8866;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_QUERY_RESULT_AVAILABLE = 0x8867;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_ANY_SAMPLES_PASSED = 0x8c2f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_ANY_SAMPLES_PASSED_CONSERVATIVE = 0x8d6a;\r\n// Draw buffers\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_DRAW_BUFFERS = 0x8824;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER0 = 0x8825;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER1 = 0x8826;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER2 = 0x8827;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER3 = 0x8828;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER4 = 0x8829;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER5 = 0x882a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER6 = 0x882b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER7 = 0x882c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER8 = 0x882d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER9 = 0x882e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER10 = 0x882f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER11 = 0x8830;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER12 = 0x8831;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER13 = 0x8832;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER14 = 0x8833;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER15 = 0x8834;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_COLOR_ATTACHMENTS = 0x8cdf;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT1 = 0x8ce1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT2 = 0x8ce2;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT3 = 0x8ce3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT4 = 0x8ce4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT5 = 0x8ce5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT6 = 0x8ce6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT7 = 0x8ce7;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT8 = 0x8ce8;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT9 = 0x8ce9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT10 = 0x8cea;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT11 = 0x8ceb;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT12 = 0x8cec;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT13 = 0x8ced;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT14 = 0x8cee;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT15 = 0x8cef;\r\n// Samplers\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_3D = 0x8b5f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_2D_SHADOW = 0x8b62;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_2D_ARRAY = 0x8dc1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_2D_ARRAY_SHADOW = 0x8dc4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_CUBE_SHADOW = 0x8dc5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_SAMPLER_2D = 0x8dca;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_SAMPLER_3D = 0x8dcb;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_SAMPLER_CUBE = 0x8dcc;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_SAMPLER_2D_ARRAY = 0x8dcf;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_SAMPLER_2D = 0x8dd2;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_SAMPLER_3D = 0x8dd3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_SAMPLER_CUBE = 0x8dd4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_SAMPLER_2D_ARRAY = 0x8dd7;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_SAMPLES = 0x8d57;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_BINDING = 0x8919;\r\n// Buffers\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PIXEL_PACK_BUFFER = 0x88eb;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PIXEL_UNPACK_BUFFER = 0x88ec;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PIXEL_PACK_BUFFER_BINDING = 0x88ed;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PIXEL_UNPACK_BUFFER_BINDING = 0x88ef;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COPY_READ_BUFFER = 0x8f36;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COPY_WRITE_BUFFER = 0x8f37;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COPY_READ_BUFFER_BINDING = 0x8f36;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COPY_WRITE_BUFFER_BINDING = 0x8f37;\r\n// Data types\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT2X3 = 0x8b65;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT2X4 = 0x8b66;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT3X2 = 0x8b67;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT3X4 = 0x8b68;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT4X2 = 0x8b69;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT4X3 = 0x8b6a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_VEC2 = 0x8dc6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_VEC3 = 0x8dc7;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_VEC4 = 0x8dc8;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_NORMALIZED = 0x8c17;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SIGNED_NORMALIZED = 0x8f9c;\r\n// Vertex attributes\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_INTEGER = 0x88fd;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_DIVISOR = 0x88fe;\r\n// Transform feedback\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_BUFFER_MODE = 0x8c7f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS = 0x8c80;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_VARYINGS = 0x8c83;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_BUFFER_START = 0x8c84;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_BUFFER_SIZE = 0x8c85;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN = 0x8c88;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS = 0x8c8a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS = 0x8c8b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INTERLEAVED_ATTRIBS = 0x8c8c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SEPARATE_ATTRIBS = 0x8c8d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_BUFFER = 0x8c8e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_BUFFER_BINDING = 0x8c8f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK = 0x8e22;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_PAUSED = 0x8e23;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_ACTIVE = 0x8e24;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_BINDING = 0x8e25;\r\n// Framebuffers and renderbuffers\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING = 0x8210;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE = 0x8211;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_RED_SIZE = 0x8212;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_GREEN_SIZE = 0x8213;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_BLUE_SIZE = 0x8214;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE = 0x8215;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE = 0x8216;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE = 0x8217;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_DEFAULT = 0x8218;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH24_STENCIL8 = 0x88f0;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_FRAMEBUFFER_BINDING = 0x8ca6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_READ_FRAMEBUFFER = 0x8ca8;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_FRAMEBUFFER = 0x8ca9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_READ_FRAMEBUFFER_BINDING = 0x8caa;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_SAMPLES = 0x8cab;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER = 0x8cd4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE = 0x8d56;\r\n// Uniforms\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BUFFER = 0x8a11;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BUFFER_BINDING = 0x8a28;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BUFFER_START = 0x8a29;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BUFFER_SIZE = 0x8a2a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VERTEX_UNIFORM_BLOCKS = 0x8a2b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_FRAGMENT_UNIFORM_BLOCKS = 0x8a2d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_COMBINED_UNIFORM_BLOCKS = 0x8a2e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_UNIFORM_BUFFER_BINDINGS = 0x8a2f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_UNIFORM_BLOCK_SIZE = 0x8a30;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS = 0x8a31;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS = 0x8a33;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BUFFER_OFFSET_ALIGNMENT = 0x8a34;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_ACTIVE_UNIFORM_BLOCKS = 0x8a36;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_TYPE = 0x8a37;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_SIZE = 0x8a38;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_INDEX = 0x8a3a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_OFFSET = 0x8a3b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_ARRAY_STRIDE = 0x8a3c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_MATRIX_STRIDE = 0x8a3d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_IS_ROW_MAJOR = 0x8a3e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_BINDING = 0x8a3f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_DATA_SIZE = 0x8a40;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_ACTIVE_UNIFORMS = 0x8a42;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES = 0x8a43;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER = 0x8a44;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER = 0x8a46;\r\n// Sync objects\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_OBJECT_TYPE = 0x9112;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SYNC_CONDITION = 0x9113;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SYNC_STATUS = 0x9114;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SYNC_FLAGS = 0x9115;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SYNC_FENCE = 0x9116;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SYNC_GPU_COMMANDS_COMPLETE = 0x9117;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNALED = 0x9118;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SIGNALED = 0x9119;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_ALREADY_SIGNALED = 0x911a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TIMEOUT_EXPIRED = 0x911b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_CONDITION_SATISFIED = 0x911c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_WAIT_FAILED = 0x911d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SYNC_FLUSH_COMMANDS_BIT = 0x00000001;\r\n// Miscellaneous constants\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR = 0x1800;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH = 0x1801;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL = 0x1802;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MIN = 0x8007;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX = 0x8008;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_COMPONENT24 = 0x81a6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STREAM_READ = 0x88e1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STREAM_COPY = 0x88e2;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STATIC_READ = 0x88e5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STATIC_COPY = 0x88e6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DYNAMIC_READ = 0x88e9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DYNAMIC_COPY = 0x88ea;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_COMPONENT32F = 0x8cac;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH32F_STENCIL8 = 0x8cad;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INVALID_INDEX = 0xffffffff;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TIMEOUT_IGNORED = -1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_CLIENT_WAIT_TIMEOUT_WEBGL = 0x9247;\r\n// Constants defined in WebGL extensions\r\n// ANGLE_instanced_arrays\r\n// The ANGLE_instanced_arrays extension is part of the WebGL API and allows to draw the same object, or groups of similar objects multiple times, if they share the same vertex data, primitive count and type\r\n/**\r\n * Describes the frequency divisor used for instanced rendering\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE = 0x88fe;\r\n// WEBGL_debug_renderer_info\r\n// The WEBGL_debug_renderer_info extension is part of the WebGL API and exposes two constants with information about the graphics driver for debugging purposes\r\n/**\r\n * Passed to getParameter to get the vendor string of the graphics driver\r\n * @constant {number}\r\n */\r\nconst GL_UNMASKED_VENDOR_WEBGL = 0x9245;\r\n/**\r\n * Passed to getParameter to get the renderer string of the graphics driver\r\n * @constant {number}\r\n */\r\nconst GL_UNMASKED_RENDERER_WEBGL = 0x9246;\r\n// EXT_texture_filter_anisotropic\r\n// The EXT_texture_filter_anisotropic extension is part of the WebGL API and exposes two constants for anisotropic filtering (AF)\r\n/**\r\n * Returns the maximum available anisotropy\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TEXTURE_MAX_ANISOTROPY_EXT = 0x84ff;\r\n/**\r\n * Passed to texParameter to set the desired maximum anisotropy for a texture\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_MAX_ANISOTROPY_EXT = 0x84fe;\r\n// WEBGL_compressed_texture_s3tc\r\n// The WEBGL_compressed_texture_s3tc extension is part of the WebGL API and exposes four S3TC compressed texture formats\r\n/**\r\n * A DXT1-compressed image in an RGB image format\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB_S3TC_DXT1_EXT = 0x83f0;\r\n/**\r\n * A DXT1-compressed image in an RGB image format with a simple on/off alpha value\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_S3TC_DXT1_EXT = 0x83f1;\r\n/**\r\n * A DXT3-compressed image in an RGBA image format. Compared to a 32-bit RGBA texture, it offers 4:1 compression\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_S3TC_DXT3_EXT = 0x83f2;\r\n/**\r\n * A DXT5-compressed image in an RGBA image format. It also provides a 4:1 compression, but differs to the DXT3 compression in how the alpha compression is done\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_S3TC_DXT5_EXT = 0x83f3;\r\n// WEBGL_compressed_texture_s3tc_srgb\r\n// The WEBGL_compressed_texture_s3tc_srgb extension is part of the WebGL API and exposes four S3TC compressed texture formats for the sRGB colorspace\r\n/**\r\n * A DXT1-compressed image in an sRGB image format\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB_S3TC_DXT1_EXT = 0x8c4c;\r\n/**\r\n * A DXT1-compressed image in an sRGB image format with a simple on/off alpha value\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT = 0x8c4d;\r\n/**\r\n * A DXT3-compressed image in an sRGBA image format\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT = 0x8c4e;\r\n/**\r\n * A DXT5-compressed image in an sRGBA image format\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT = 0x8c4f;\r\n// WEBGL_compressed_texture_etc\r\n// The WEBGL_compressed_texture_etc extension is part of the WebGL API and exposes 10 ETC/EAC compressed texture formats\r\n/**\r\n * One-channel (red) unsigned format compression\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_R11_EAC = 0x9270;\r\n/**\r\n * One-channel (red) signed format compression\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SIGNED_R11_EAC = 0x9271;\r\n/**\r\n * Two-channel (red and green) unsigned format compression\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RG11_EAC = 0x9272;\r\n/**\r\n * Two-channel (red and green) signed format compression\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SIGNED_RG11_EAC = 0x9273;\r\n/**\r\n * Compresses RBG8 data with no alpha channel\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB8_ETC2 = 0x9274;\r\n/**\r\n * Compresses RGBA8 data. The RGB part is encoded the same as RGB_ETC2, but the alpha part is encoded separately\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA8_ETC2_EAC = 0x9275;\r\n/**\r\n * Compresses sRBG8 data with no alpha channel\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ETC2 = 0x9276;\r\n/**\r\n * Compresses sRGBA8 data. The sRGB part is encoded the same as SRGB_ETC2, but the alpha part is encoded separately\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ETC2_EAC = 0x9277;\r\n/**\r\n * Similar to RGB8_ETC, but with ability to punch through the alpha channel, which means to make it completely opaque or transparent\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 0x9278;\r\n/**\r\n * Similar to SRGB8_ETC, but with ability to punch through the alpha channel, which means to make it completely opaque or transparent\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 0x9279;\r\n// WEBGL_compressed_texture_pvrtc\r\n// The WEBGL_compressed_texture_pvrtc extension is part of the WebGL API and exposes four PVRTC compressed texture formats\r\n/**\r\n * RGB compression in 4-bit mode. One block for each 4×4 pixels\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB_PVRTC_4BPPV1_IMG = 0x8c00;\r\n/**\r\n * RGBA compression in 4-bit mode. One block for each 4×4 pixels\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_PVRTC_4BPPV1_IMG = 0x8c02;\r\n/**\r\n * RGB compression in 2-bit mode. One block for each 8×4 pixels\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB_PVRTC_2BPPV1_IMG = 0x8c01;\r\n/**\r\n * RGBA compression in 2-bit mode. One block for each 8×4 pixels\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_PVRTC_2BPPV1_IMG = 0x8c03;\r\n// WEBGL_compressed_texture_etc1\r\n// The WEBGL_compressed_texture_etc1 extension is part of the WebGL API and exposes the ETC1 compressed texture format\r\n/**\r\n * Compresses 24-bit RGB data with no alpha channel\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB_ETC1_WEBGL = 0x8d64;\r\n// WEBGL_compressed_texture_atc\r\n// The WEBGL_compressed_texture_atc extension is part of the WebGL API and exposes 3 ATC compressed texture formats. ATC is a proprietary compression algorithm for compressing textures on handheld devices\r\n/**\r\n * Compresses RGB textures with no alpha channel\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB_ATC_WEBGL = 0x8c92;\r\n/**\r\n * Compresses RGBA textures using explicit alpha encoding (useful when alpha transitions are sharp)\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL = 0x8c92;\r\n/**\r\n * Compresses RGBA textures using interpolated alpha encoding (useful when alpha transitions are gradient)\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL = 0x87ee;\r\n// WEBGL_compressed_texture_astc\r\n// The WEBGL_compressed_texture_astc extension is part of the WebGL API and exposes Adaptive Scalable Texture Compression (ASTC) compressed texture formats to WebGL\r\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\r\n// https://developer.nvidia.com/astc-texture-compression-for-game-assets\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 4x4\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_4X4_KHR = 0x93b0;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 5x4\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_5X4_KHR = 0x93b1;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 5x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_5X5_KHR = 0x93b2;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 6x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_6X5_KHR = 0x93b3;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 6x6\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_6X6_KHR = 0x93b4;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 8x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_8X5_KHR = 0x93b5;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 8x6\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_8X6_KHR = 0x93b6;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 8x8\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_8X8_KHR = 0x93b7;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 10x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_10X5_KHR = 0x93b8;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 10x6\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_10X6_KHR = 0x93b9;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 10x8\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_10X8_KHR = 0x93ba;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 10x10\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_10X10_KHR = 0x93bb;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 12x10\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_12X10_KHR = 0x93bc;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 12x12\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_12X12_KHR = 0x93bd;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 4x4\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR = 0x93d0;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 5x4\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR = 0x93d1;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 5x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR = 0x93d2;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 6x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR = 0x93d3;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 6x6\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR = 0x93d4;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 8x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR = 0x93d5;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 8x6\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR = 0x93d6;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 8x8\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR = 0x93d7;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 10x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR = 0x93d8;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 10x6\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR = 0x93d9;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 10x8\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR = 0x93da;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 10x10\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR = 0x93db;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 12x10\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR = 0x93dc;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 12x12\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR = 0x93dd;\r\n// WEBGL_depth_texture\r\n// The WEBGL_depth_texture extension is part of the WebGL API and defines 2D depth and depth-stencil textures\r\n/**\r\n * Unsigned integer type for 24-bit depth texture data\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_24_8_WEBGL = 0x84fa;\r\n// OES_texture_half_float\r\n// The OES_texture_half_float extension is part of the WebGL API and adds texture formats with 16- (aka half float) and 32-bit floating-point components\r\n/**\r\n * Half floating-point type (16-bit)\r\n * @constant {number}\r\n */\r\nconst GL_HALF_FLOAT_OES = 0x8d61;\r\n// WEBGL_color_buffer_float\r\n// The WEBGL_color_buffer_float extension is part of the WebGL API and adds the ability to render to 32-bit floating-point color buffers\r\n/**\r\n * RGBA 32-bit floating-point color-renderable format\r\n * @constant {number}\r\n */\r\nconst GL_RGBA32F_EXT = 0x8814;\r\n/**\r\n * RGB 32-bit floating-point color-renderable format\r\n * @constant {number}\r\n */\r\nconst GL_RGB32F_EXT = 0x8815;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE_EXT = 0x8211;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_NORMALIZED_EXT = 0x8c17;\r\n// EXT_blend_minmax\r\n// The EXT_blend_minmax extension is part of the WebGL API and extends blending capabilities by adding two new blend equations: the minimum or maximum color components of the source and destination colors\r\n/**\r\n * Produces the minimum color components of the source and destination colors\r\n * @constant {number}\r\n */\r\nconst GL_MIN_EXT = 0x8007;\r\n/**\r\n * Produces the maximum color components of the source and destination colors\r\n * @constant {number}\r\n */\r\nconst GL_MAX_EXT = 0x8008;\r\n// EXT_sRGB\r\n// The EXT_sRGB extension is part of the WebGL API and adds sRGB support to textures and framebuffer objects\r\n/**\r\n * Unsized sRGB format that leaves the precision up to the driver\r\n * @constant {number}\r\n */\r\nconst GL_SRGB_EXT = 0x8c40;\r\n/**\r\n * Unsized sRGB format with unsized alpha component\r\n * @constant {number}\r\n */\r\nconst GL_SRGB_ALPHA_EXT = 0x8c42;\r\n/**\r\n * Sized (8-bit) sRGB and alpha formats\r\n * @constant {number}\r\n */\r\nconst GL_SRGB8_ALPHA8_EXT = 0x8c43;\r\n/**\r\n * Returns the framebuffer color encoding\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING_EXT = 0x8210;\r\n// OES_standard_derivatives\r\n// The OES_standard_derivatives extension is part of the WebGL API and adds the GLSL derivative functions dFdx, dFdy, and fwidth\r\n/**\r\n * Indicates the accuracy of the derivative calculation for the GLSL built-in functions: dFdx, dFdy, and fwidth\r\n * @constant {number}\r\n */\r\nconst GL_FRAGMENT_SHADER_DERIVATIVE_HINT_OES = 0x8b8b;\r\n// WEBGL_draw_buffers\r\n// The WEBGL_draw_buffers extension is part of the WebGL API and enables a fragment shader to write to several textures, which is useful for deferred shading, for example\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT0_WEBGL = 0x8ce0;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT1_WEBGL = 0x8ce1;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT2_WEBGL = 0x8ce2;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT3_WEBGL = 0x8ce3;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT4_WEBGL = 0x8ce4;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT5_WEBGL = 0x8ce5;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT6_WEBGL = 0x8ce6;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT7_WEBGL = 0x8ce7;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT8_WEBGL = 0x8ce8;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT9_WEBGL = 0x8ce9;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT10_WEBGL = 0x8cea;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT11_WEBGL = 0x8ceb;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT12_WEBGL = 0x8cec;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT13_WEBGL = 0x8ced;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT14_WEBGL = 0x8cee;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT15_WEBGL = 0x8cef;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER0_WEBGL = 0x8825;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER1_WEBGL = 0x8826;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER2_WEBGL = 0x8827;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER3_WEBGL = 0x8828;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER4_WEBGL = 0x8829;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER5_WEBGL = 0x882a;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER6_WEBGL = 0x882b;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER7_WEBGL = 0x882c;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER8_WEBGL = 0x882d;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER9_WEBGL = 0x882e;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER10_WEBGL = 0x882f;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER11_WEBGL = 0x8830;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER12_WEBGL = 0x8831;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER13_WEBGL = 0x8832;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER14_WEBGL = 0x8833;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER15_WEBGL = 0x8834;\r\n/**\r\n * Maximum number of framebuffer color attachment points\r\n * @constant {number}\r\n */\r\nconst GL_MAX_COLOR_ATTACHMENTS_WEBGL = 0x8cdf;\r\n/**\r\n * Maximum number of draw buffers\r\n * @constant {number}\r\n */\r\nconst GL_MAX_DRAW_BUFFERS_WEBGL = 0x8824;\r\n// OES_vertex_array_object\r\n// The OES_vertex_array_object extension is part of the WebGL API and provides vertex array objects (VAOs) which encapsulate vertex array states. These objects keep pointers to vertex data and provide names for different sets of vertex data\r\n/**\r\n * The bound vertex array object (VAO)\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ARRAY_BINDING_OES = 0x85b5;\r\n// EXT_disjoint_timer_query\r\n// The EXT_disjoint_timer_query extension is part of the WebGL API and provides a way to measure the duration of a set of GL commands, without stalling the rendering pipeline\r\n/**\r\n * The number of bits used to hold the query result for the given target\r\n * @constant {number}\r\n */\r\nconst GL_QUERY_COUNTER_BITS_EXT = 0x8864;\r\n/**\r\n * The currently active query\r\n * @constant {number}\r\n */\r\nconst GL_CURRENT_QUERY_EXT = 0x8865;\r\n/**\r\n * The query result\r\n * @constant {number}\r\n */\r\nconst GL_QUERY_RESULT_EXT = 0x8866;\r\n/**\r\n * A Boolean indicating whether or not a query result is available\r\n * @constant {number}\r\n */\r\nconst GL_QUERY_RESULT_AVAILABLE_EXT = 0x8867;\r\n/**\r\n * Elapsed time (in nanoseconds)\r\n * @constant {number}\r\n */\r\nconst GL_TIME_ELAPSED_EXT = 0x88bf;\r\n/**\r\n * The current time\r\n * @constant {number}\r\n */\r\nconst GL_TIMESTAMP_EXT = 0x8e28;\r\n/**\r\n * A Boolean indicating whether or not the GPU performed any disjoint operation\r\n * @constant {number}\r\n */\r\nconst GL_GPU_DISJOINT_EXT = 0x8fbb;\r\n// Constants defined in WebGL draft extensions\r\n// KHR_parallel_shader_compile\r\n// The KHR_parallel_shader_compile extension is part of the WebGL draft API and provides multithreaded asynchronous shader compilation\r\n/**\r\n * Query to determine if the compilation process is complete\r\n * @constant {number}\r\n */\r\nconst GL_COMPLETION_STATUS_KHR = 0x91b1;\n\nexport { GL_ACTIVE_ATTRIBUTES, GL_ACTIVE_TEXTURE, GL_ACTIVE_UNIFORMS, GL_ACTIVE_UNIFORM_BLOCKS, GL_ALIASED_LINE_WIDTH_RANGE, GL_ALIASED_POINT_SIZE_RANGE, GL_ALPHA, GL_ALPHA_BITS, GL_ALREADY_SIGNALED, GL_ALWAYS, GL_ANY_SAMPLES_PASSED, GL_ANY_SAMPLES_PASSED_CONSERVATIVE, GL_ARRAY_BUFFER, GL_ARRAY_BUFFER_BINDING, GL_ATTACHED_SHADERS, GL_BACK, GL_BLEND, GL_BLEND_COLOR, GL_BLEND_DST_ALPHA, GL_BLEND_DST_RGB, GL_BLEND_EQUATION, GL_BLEND_EQUATION_ALPHA, GL_BLEND_EQUATION_RGB, GL_BLEND_SRC_ALPHA, GL_BLEND_SRC_RGB, GL_BLUE_BITS, GL_BOOL, GL_BOOL_VEC2, GL_BOOL_VEC3, GL_BOOL_VEC4, GL_BROWSER_DEFAULT_WEBGL, GL_BUFFER_SIZE, GL_BUFFER_USAGE, GL_BYTE, GL_CCW, GL_CLAMP_TO_EDGE, GL_COLOR, GL_COLOR_ATTACHMENT0, GL_COLOR_ATTACHMENT0_WEBGL, GL_COLOR_ATTACHMENT1, GL_COLOR_ATTACHMENT10, GL_COLOR_ATTACHMENT10_WEBGL, GL_COLOR_ATTACHMENT11, GL_COLOR_ATTACHMENT11_WEBGL, GL_COLOR_ATTACHMENT12, GL_COLOR_ATTACHMENT12_WEBGL, GL_COLOR_ATTACHMENT13, GL_COLOR_ATTACHMENT13_WEBGL, GL_COLOR_ATTACHMENT14, GL_COLOR_ATTACHMENT14_WEBGL, GL_COLOR_ATTACHMENT15, GL_COLOR_ATTACHMENT15_WEBGL, GL_COLOR_ATTACHMENT1_WEBGL, GL_COLOR_ATTACHMENT2, GL_COLOR_ATTACHMENT2_WEBGL, GL_COLOR_ATTACHMENT3, GL_COLOR_ATTACHMENT3_WEBGL, GL_COLOR_ATTACHMENT4, GL_COLOR_ATTACHMENT4_WEBGL, GL_COLOR_ATTACHMENT5, GL_COLOR_ATTACHMENT5_WEBGL, GL_COLOR_ATTACHMENT6, GL_COLOR_ATTACHMENT6_WEBGL, GL_COLOR_ATTACHMENT7, GL_COLOR_ATTACHMENT7_WEBGL, GL_COLOR_ATTACHMENT8, GL_COLOR_ATTACHMENT8_WEBGL, GL_COLOR_ATTACHMENT9, GL_COLOR_ATTACHMENT9_WEBGL, GL_COLOR_BUFFER_BIT, GL_COLOR_CLEAR_VALUE, GL_COLOR_WRITEMASK, GL_COMPARE_REF_TO_TEXTURE, GL_COMPILE_STATUS, GL_COMPLETION_STATUS_KHR, GL_COMPRESSED_R11_EAC, GL_COMPRESSED_RG11_EAC, GL_COMPRESSED_RGB8_ETC2, GL_COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2, GL_COMPRESSED_RGBA8_ETC2_EAC, GL_COMPRESSED_RGBA_ASTC_10X10_KHR, GL_COMPRESSED_RGBA_ASTC_10X5_KHR, GL_COMPRESSED_RGBA_ASTC_10X6_KHR, GL_COMPRESSED_RGBA_ASTC_10X8_KHR, GL_COMPRESSED_RGBA_ASTC_12X10_KHR, GL_COMPRESSED_RGBA_ASTC_12X12_KHR, GL_COMPRESSED_RGBA_ASTC_4X4_KHR, GL_COMPRESSED_RGBA_ASTC_5X4_KHR, GL_COMPRESSED_RGBA_ASTC_5X5_KHR, GL_COMPRESSED_RGBA_ASTC_6X5_KHR, GL_COMPRESSED_RGBA_ASTC_6X6_KHR, GL_COMPRESSED_RGBA_ASTC_8X5_KHR, GL_COMPRESSED_RGBA_ASTC_8X6_KHR, GL_COMPRESSED_RGBA_ASTC_8X8_KHR, GL_COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL, GL_COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL, GL_COMPRESSED_RGBA_PVRTC_2BPPV1_IMG, GL_COMPRESSED_RGBA_PVRTC_4BPPV1_IMG, GL_COMPRESSED_RGBA_S3TC_DXT1_EXT, GL_COMPRESSED_RGBA_S3TC_DXT3_EXT, GL_COMPRESSED_RGBA_S3TC_DXT5_EXT, GL_COMPRESSED_RGB_ATC_WEBGL, GL_COMPRESSED_RGB_ETC1_WEBGL, GL_COMPRESSED_RGB_PVRTC_2BPPV1_IMG, GL_COMPRESSED_RGB_PVRTC_4BPPV1_IMG, GL_COMPRESSED_RGB_S3TC_DXT1_EXT, GL_COMPRESSED_SIGNED_R11_EAC, GL_COMPRESSED_SIGNED_RG11_EAC, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ETC2_EAC, GL_COMPRESSED_SRGB8_ETC2, GL_COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2, GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT, GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT, GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT, GL_COMPRESSED_SRGB_S3TC_DXT1_EXT, GL_COMPRESSED_TEXTURE_FORMATS, GL_CONDITION_SATISFIED, GL_CONSTANT_ALPHA, GL_CONSTANT_COLOR, GL_CONTEXT_LOST_WEBGL, GL_COPY_READ_BUFFER, GL_COPY_READ_BUFFER_BINDING, GL_COPY_WRITE_BUFFER, GL_COPY_WRITE_BUFFER_BINDING, GL_CULL_FACE, GL_CULL_FACE_MODE, GL_CURRENT_PROGRAM, GL_CURRENT_QUERY, GL_CURRENT_QUERY_EXT, GL_CURRENT_VERTEX_ATTRIB, GL_CW, GL_DECR, GL_DECR_WRAP, GL_DELETE_STATUS, GL_DEPTH, GL_DEPTH24_STENCIL8, GL_DEPTH32F_STENCIL8, GL_DEPTH_ATTACHMENT, GL_DEPTH_BITS, GL_DEPTH_BUFFER_BIT, GL_DEPTH_CLEAR_VALUE, GL_DEPTH_COMPONENT, GL_DEPTH_COMPONENT16, GL_DEPTH_COMPONENT24, GL_DEPTH_COMPONENT32F, GL_DEPTH_FUNC, GL_DEPTH_RANGE, GL_DEPTH_STENCIL, GL_DEPTH_STENCIL_ATTACHMENT, GL_DEPTH_TEST, GL_DEPTH_WRITEMASK, GL_DITHER, GL_DONT_CARE, GL_DRAW_BUFFER0, GL_DRAW_BUFFER0_WEBGL, GL_DRAW_BUFFER1, GL_DRAW_BUFFER10, GL_DRAW_BUFFER10_WEBGL, GL_DRAW_BUFFER11, GL_DRAW_BUFFER11_WEBGL, GL_DRAW_BUFFER12, GL_DRAW_BUFFER12_WEBGL, GL_DRAW_BUFFER13, GL_DRAW_BUFFER13_WEBGL, GL_DRAW_BUFFER14, GL_DRAW_BUFFER14_WEBGL, GL_DRAW_BUFFER15, GL_DRAW_BUFFER15_WEBGL, GL_DRAW_BUFFER1_WEBGL, GL_DRAW_BUFFER2, GL_DRAW_BUFFER2_WEBGL, GL_DRAW_BUFFER3, GL_DRAW_BUFFER3_WEBGL, GL_DRAW_BUFFER4, GL_DRAW_BUFFER4_WEBGL, GL_DRAW_BUFFER5, GL_DRAW_BUFFER5_WEBGL, GL_DRAW_BUFFER6, GL_DRAW_BUFFER6_WEBGL, GL_DRAW_BUFFER7, GL_DRAW_BUFFER7_WEBGL, GL_DRAW_BUFFER8, GL_DRAW_BUFFER8_WEBGL, GL_DRAW_BUFFER9, GL_DRAW_BUFFER9_WEBGL, GL_DRAW_FRAMEBUFFER, GL_DRAW_FRAMEBUFFER_BINDING, GL_DST_ALPHA, GL_DST_COLOR, GL_DYNAMIC_COPY, GL_DYNAMIC_DRAW, GL_DYNAMIC_READ, GL_ELEMENT_ARRAY_BUFFER, GL_ELEMENT_ARRAY_BUFFER_BINDING, GL_EQUAL, GL_FASTEST, GL_FLOAT, GL_FLOAT_32_UNSIGNED_INT_24_8_REV, GL_FLOAT_MAT2, GL_FLOAT_MAT2X3, GL_FLOAT_MAT2X4, GL_FLOAT_MAT3, GL_FLOAT_MAT3X2, GL_FLOAT_MAT3X4, GL_FLOAT_MAT4, GL_FLOAT_MAT4X2, GL_FLOAT_MAT4X3, GL_FLOAT_VEC2, GL_FLOAT_VEC3, GL_FLOAT_VEC4, GL_FRAGMENT_SHADER, GL_FRAGMENT_SHADER_DERIVATIVE_HINT, GL_FRAGMENT_SHADER_DERIVATIVE_HINT_OES, GL_FRAMEBUFFER, GL_FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE, GL_FRAMEBUFFER_ATTACHMENT_BLUE_SIZE, GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING, GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING_EXT, GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE, GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE_EXT, GL_FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE, GL_FRAMEBUFFER_ATTACHMENT_GREEN_SIZE, GL_FRAMEBUFFER_ATTACHMENT_OBJECT_NAME, GL_FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE, GL_FRAMEBUFFER_ATTACHMENT_RED_SIZE, GL_FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE, GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE, GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER, GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL, GL_FRAMEBUFFER_BINDING, GL_FRAMEBUFFER_COMPLETE, GL_FRAMEBUFFER_DEFAULT, GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT, GL_FRAMEBUFFER_INCOMPLETE_DIMENSIONS, GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT, GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE, GL_FRAMEBUFFER_UNSUPPORTED, GL_FRONT, GL_FRONT_AND_BACK, GL_FRONT_FACE, GL_FUNC_ADD, GL_FUNC_REVERSE_SUBTRACT, GL_FUNC_SUBSTRACT, GL_GENERATE_MIPMAP_HINT, GL_GEQUAL, GL_GPU_DISJOINT_EXT, GL_GREATER, GL_GREEN_BITS, GL_HALF_FLOAT, GL_HALF_FLOAT_OES, GL_HIGH_FLOAT, GL_HIGH_INT, GL_IMPLEMENTATION_COLOR_READ_FORMAT, GL_IMPLEMENTATION_COLOR_READ_TYPE, GL_INCR, GL_INCR_WRAP, GL_INT, GL_INTERLEAVED_ATTRIBS, GL_INT_2_10_10_10_REV, GL_INT_SAMPLER_2D, GL_INT_SAMPLER_2D_ARRAY, GL_INT_SAMPLER_3D, GL_INT_SAMPLER_CUBE, GL_INT_VEC2, GL_INT_VEC3, GL_INT_VEC4, GL_INVALID_ENUM, GL_INVALID_FRAMEBUFFER_OPERATION, GL_INVALID_INDEX, GL_INVALID_OPERATION, GL_INVALID_VALUE, GL_INVERT, GL_KEEP, GL_LEQUAL, GL_LESS, GL_LINEAR, GL_LINEAR_MIPMAP_LINEAR, GL_LINEAR_MIPMAP_NEAREST, GL_LINES, GL_LINE_LOOP, GL_LINE_STRIP, GL_LINE_WIDTH, GL_LINK_STATUS, GL_LOW_FLOAT, GL_LOW_INT, GL_LUMINANCE, GL_LUMINANCE_ALPHA, GL_MAX, GL_MAX_3D_TEXTURE_SIZE, GL_MAX_ARRAY_TEXTURE_LAYERS, GL_MAX_CLIENT_WAIT_TIMEOUT_WEBGL, GL_MAX_COLOR_ATTACHMENTS, GL_MAX_COLOR_ATTACHMENTS_WEBGL, GL_MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS, GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS, GL_MAX_COMBINED_UNIFORM_BLOCKS, GL_MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS, GL_MAX_CUBE_MAP_TEXTURE_SIZE, GL_MAX_DRAW_BUFFERS, GL_MAX_DRAW_BUFFERS_WEBGL, GL_MAX_ELEMENTS_INDICES, GL_MAX_ELEMENTS_VERTICES, GL_MAX_ELEMENT_INDEX, GL_MAX_EXT, GL_MAX_FRAGMENT_INPUT_COMPONENTS, GL_MAX_FRAGMENT_UNIFORM_BLOCKS, GL_MAX_FRAGMENT_UNIFORM_COMPONENTS, GL_MAX_FRAGMENT_UNIFORM_VECTORS, GL_MAX_PROGRAM_TEXEL_OFFSET, GL_MAX_RENDERBUFFER_SIZE, GL_MAX_SAMPLES, GL_MAX_SERVER_WAIT_TIMEOUT, GL_MAX_TEXTURE_IMAGE_UNITS, GL_MAX_TEXTURE_LOD_BIAS, GL_MAX_TEXTURE_MAX_ANISOTROPY_EXT, GL_MAX_TEXTURE_SIZE, GL_MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS, GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS, GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS, GL_MAX_UNIFORM_BLOCK_SIZE, GL_MAX_UNIFORM_BUFFER_BINDINGS, GL_MAX_VARYING_COMPONENTS, GL_MAX_VARYING_VECTORS, GL_MAX_VERTEX_ATTRIBS, GL_MAX_VERTEX_OUTPUT_COMPONENTS, GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS, GL_MAX_VERTEX_UNIFORM_BLOCKS, GL_MAX_VERTEX_UNIFORM_COMPONENTS, GL_MAX_VERTEX_UNIFORM_VECTORS, GL_MAX_VIEWPORT_DIMS, GL_MEDIUM_FLOAT, GL_MEDIUM_INT, GL_MIN, GL_MIN_EXT, GL_MIN_PROGRAM_TEXEL_OFFSET, GL_MIRRORED_REPEAT, GL_NEAREST, GL_NEAREST_MIPMAP_LINEAR, GL_NEAREST_MIPMAP_NEAREST, GL_NEVER, GL_NICEST, GL_NONE, GL_NOTEQUAL, GL_NO_ERROR, GL_OBJECT_TYPE, GL_ONE, GL_ONE_MINUS_CONSTANT_ALPHA, GL_ONE_MINUS_CONSTANT_COLOR, GL_ONE_MINUS_DST_ALPHA, GL_ONE_MINUS_DST_COLOR, GL_ONE_MINUS_SRC_ALPHA, GL_ONE_MINUS_SRC_COLOR, GL_OUT_OF_MEMORY, GL_PACK_ALIGNMENT, GL_PACK_ROW_LENGTH, GL_PACK_SKIP_PIXELS, GL_PACK_SKIP_ROWS, GL_PIXEL_PACK_BUFFER, GL_PIXEL_PACK_BUFFER_BINDING, GL_PIXEL_UNPACK_BUFFER, GL_PIXEL_UNPACK_BUFFER_BINDING, GL_POINTS, GL_POLYGON_OFFSET_FACTOR, GL_POLYGON_OFFSET_FILL, GL_POLYGON_OFFSET_UNITS, GL_QUERY_COUNTER_BITS_EXT, GL_QUERY_RESULT, GL_QUERY_RESULT_AVAILABLE, GL_QUERY_RESULT_AVAILABLE_EXT, GL_QUERY_RESULT_EXT, GL_R11F_G11F_B10F, GL_R16F, GL_R16I, GL_R16UI, GL_R32F, GL_R32I, GL_R32UI, GL_R8, GL_R8I, GL_R8UI, GL_R8_SNORM, GL_RASTERIZER_DISCARD, GL_READ_BUFFER, GL_READ_FRAMEBUFFER, GL_READ_FRAMEBUFFER_BINDING, GL_RED, GL_RED_BITS, GL_RED_INTEGER, GL_RENDERBUFFER, GL_RENDERBUFFER_ALPHA_SIZE, GL_RENDERBUFFER_BINDING, GL_RENDERBUFFER_BLUE_SIZE, GL_RENDERBUFFER_DEPTH_SIZE, GL_RENDERBUFFER_GREEN_SIZE, GL_RENDERBUFFER_HEIGHT, GL_RENDERBUFFER_INTERNAL_FORMAT, GL_RENDERBUFFER_RED_SIZE, GL_RENDERBUFFER_SAMPLES, GL_RENDERBUFFER_STENCIL_SIZE, GL_RENDERBUFFER_WIDTH, GL_RENDERER, GL_REPEAT, GL_REPLACE, GL_RG, GL_RG16F, GL_RG16I, GL_RG16UI, GL_RG32F, GL_RG32I, GL_RG32UI, GL_RG8, GL_RG8I, GL_RG8UI, GL_RG8_SNORM, GL_RGB, GL_RGB10_A2, GL_RGB10_A2UI, GL_RGB16F, GL_RGB16I, GL_RGB16UI, GL_RGB32F, GL_RGB32F_EXT, GL_RGB32I, GL_RGB32UI, GL_RGB565, GL_RGB5_A1, GL_RGB8, GL_RGB8I, GL_RGB8UI, GL_RGB8_SNORM, GL_RGB9_E5, GL_RGBA, GL_RGBA16F, GL_RGBA16I, GL_RGBA16UI, GL_RGBA32F, GL_RGBA32F_EXT, GL_RGBA32I, GL_RGBA32UI, GL_RGBA4, GL_RGBA8, GL_RGBA8I, GL_RGBA8UI, GL_RGBA8_SNORM, GL_RGBA_INTEGER, GL_RGB_INTEGER, GL_RG_INTEGER, GL_SAMPLER_2D, GL_SAMPLER_2D_ARRAY, GL_SAMPLER_2D_ARRAY_SHADOW, GL_SAMPLER_2D_SHADOW, GL_SAMPLER_3D, GL_SAMPLER_BINDING, GL_SAMPLER_CUBE, GL_SAMPLER_CUBE_SHADOW, GL_SAMPLES, GL_SAMPLE_ALPHA_TO_COVERAGE, GL_SAMPLE_BUFFERS, GL_SAMPLE_COVERAGE, GL_SAMPLE_COVERAGE_INVERT, GL_SAMPLE_COVERAGE_VALUE, GL_SCISSOR_BOX, GL_SCISSOR_TEST, GL_SEPARATE_ATTRIBS, GL_SHADER_TYPE, GL_SHADING_LANGUAGE_VERSION, GL_SHORT, GL_SIGNALED, GL_SIGNED_NORMALIZED, GL_SRC_ALPHA, GL_SRC_ALPHA_SATURATE, GL_SRC_COLOR, GL_SRGB, GL_SRGB8, GL_SRGB8_ALPHA8, GL_SRGB8_ALPHA8_EXT, GL_SRGB_ALPHA_EXT, GL_SRGB_EXT, GL_STATIC_COPY, GL_STATIC_DRAW, GL_STATIC_READ, GL_STENCIL, GL_STENCIL_ATTACHMENT, GL_STENCIL_BACK_FAIL, GL_STENCIL_BACK_FUNC, GL_STENCIL_BACK_PASS_DEPTH_FAIL, GL_STENCIL_BACK_PASS_DEPTH_PASS, GL_STENCIL_BACK_REF, GL_STENCIL_BACK_VALUE_MASK, GL_STENCIL_BACK_WRITEMASK, GL_STENCIL_BITS, GL_STENCIL_BUFFER_BIT, GL_STENCIL_CLEAR_VALUE, GL_STENCIL_FAIL, GL_STENCIL_FUNC, GL_STENCIL_INDEX, GL_STENCIL_INDEX8, GL_STENCIL_PASS_DEPTH_FAIL, GL_STENCIL_PASS_DEPTH_PASS, GL_STENCIL_REF, GL_STENCIL_TEST, GL_STENCIL_VALUE_MASK, GL_STENCIL_WRITEMASK, GL_STREAM_COPY, GL_STREAM_DRAW, GL_STREAM_READ, GL_SUBPIXEL_BITS, GL_SYNC_CONDITION, GL_SYNC_FENCE, GL_SYNC_FLAGS, GL_SYNC_FLUSH_COMMANDS_BIT, GL_SYNC_GPU_COMMANDS_COMPLETE, GL_SYNC_STATUS, GL_TEXTURE, GL_TEXTURE0, GL_TEXTURE1, GL_TEXTURE10, GL_TEXTURE11, GL_TEXTURE12, GL_TEXTURE13, GL_TEXTURE14, GL_TEXTURE15, GL_TEXTURE16, GL_TEXTURE17, GL_TEXTURE18, GL_TEXTURE19, GL_TEXTURE2, GL_TEXTURE20, GL_TEXTURE21, GL_TEXTURE22, GL_TEXTURE23, GL_TEXTURE24, GL_TEXTURE25, GL_TEXTURE26, GL_TEXTURE27, GL_TEXTURE28, GL_TEXTURE29, GL_TEXTURE3, GL_TEXTURE30, GL_TEXTURE31, GL_TEXTURE4, GL_TEXTURE5, GL_TEXTURE6, GL_TEXTURE7, GL_TEXTURE8, GL_TEXTURE9, GL_TEXTURE_2D, GL_TEXTURE_2D_ARRAY, GL_TEXTURE_3D, GL_TEXTURE_BASE_LEVEL, GL_TEXTURE_BINDING_2D, GL_TEXTURE_BINDING_2D_ARRAY, GL_TEXTURE_BINDING_3D, GL_TEXTURE_BINDING_CUBE_MAP, GL_TEXTURE_COMPARE_FUNC, GL_TEXTURE_COMPARE_MODE, GL_TEXTURE_CUBE_MAP, GL_TEXTURE_CUBE_MAP_NEGATIVE_X, GL_TEXTURE_CUBE_MAP_NEGATIVE_Y, GL_TEXTURE_CUBE_MAP_NEGATIVE_Z, GL_TEXTURE_CUBE_MAP_POSITIVE_X, GL_TEXTURE_CUBE_MAP_POSITIVE_Y, GL_TEXTURE_CUBE_MAP_POSITIVE_Z, GL_TEXTURE_IMMUTABLE_FORMAT, GL_TEXTURE_IMMUTABLE_LEVELS, GL_TEXTURE_MAG_FILTER, GL_TEXTURE_MAX_ANISOTROPY_EXT, GL_TEXTURE_MAX_LEVEL, GL_TEXTURE_MAX_LOD, GL_TEXTURE_MIN_FILTER, GL_TEXTURE_MIN_LOD, GL_TEXTURE_WRAP_R, GL_TEXTURE_WRAP_S, GL_TEXTURE_WRAP_T, GL_TIMEOUT_EXPIRED, GL_TIMEOUT_IGNORED, GL_TIMESTAMP_EXT, GL_TIME_ELAPSED_EXT, GL_TRANSFORM_FEEDBACK, GL_TRANSFORM_FEEDBACK_ACTIVE, GL_TRANSFORM_FEEDBACK_BINDING, GL_TRANSFORM_FEEDBACK_BUFFER, GL_TRANSFORM_FEEDBACK_BUFFER_BINDING, GL_TRANSFORM_FEEDBACK_BUFFER_MODE, GL_TRANSFORM_FEEDBACK_BUFFER_SIZE, GL_TRANSFORM_FEEDBACK_BUFFER_START, GL_TRANSFORM_FEEDBACK_PAUSED, GL_TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN, GL_TRANSFORM_FEEDBACK_VARYINGS, GL_TRIANGLES, GL_TRIANGLE_FAN, GL_TRIANGLE_STRIP, GL_UNIFORM_ARRAY_STRIDE, GL_UNIFORM_BLOCK_ACTIVE_UNIFORMS, GL_UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES, GL_UNIFORM_BLOCK_BINDING, GL_UNIFORM_BLOCK_DATA_SIZE, GL_UNIFORM_BLOCK_INDEX, GL_UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER, GL_UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER, GL_UNIFORM_BUFFER, GL_UNIFORM_BUFFER_BINDING, GL_UNIFORM_BUFFER_OFFSET_ALIGNMENT, GL_UNIFORM_BUFFER_SIZE, GL_UNIFORM_BUFFER_START, GL_UNIFORM_IS_ROW_MAJOR, GL_UNIFORM_MATRIX_STRIDE, GL_UNIFORM_OFFSET, GL_UNIFORM_SIZE, GL_UNIFORM_TYPE, GL_UNMASKED_RENDERER_WEBGL, GL_UNMASKED_VENDOR_WEBGL, GL_UNPACK_ALIGNMENT, GL_UNPACK_COLORSPACE_CONVERSION_WEBGL, GL_UNPACK_FLIP_Y_WEBGL, GL_UNPACK_IMAGE_HEIGHT, GL_UNPACK_PREMULTIPLY_ALPHA_WEBGL, GL_UNPACK_ROW_LENGTH, GL_UNPACK_SKIP_IMAGES, GL_UNPACK_SKIP_PIXELS, GL_UNPACK_SKIP_ROWS, GL_UNSIGNALED, GL_UNSIGNED_BYTE, GL_UNSIGNED_INT, GL_UNSIGNED_INT_10F_11F_11F_REV, GL_UNSIGNED_INT_24_8, GL_UNSIGNED_INT_24_8_WEBGL, GL_UNSIGNED_INT_2_10_10_10_REV, GL_UNSIGNED_INT_5_9_9_9_REV, GL_UNSIGNED_INT_SAMPLER_2D, GL_UNSIGNED_INT_SAMPLER_2D_ARRAY, GL_UNSIGNED_INT_SAMPLER_3D, GL_UNSIGNED_INT_SAMPLER_CUBE, GL_UNSIGNED_INT_VEC2, GL_UNSIGNED_INT_VEC3, GL_UNSIGNED_INT_VEC4, GL_UNSIGNED_NORMALIZED, GL_UNSIGNED_NORMALIZED_EXT, GL_UNSIGNED_SHORT, GL_UNSIGNED_SHORT_4_4_4_4, GL_UNSIGNED_SHORT_5_5_5_1, GL_UNSIGNED_SHORT_5_6_5, GL_VALIDATE_STATUS, GL_VENDOR, GL_VERSION, GL_VERTEX_ARRAY_BINDING, GL_VERTEX_ARRAY_BINDING_OES, GL_VERTEX_ATTRIB_ARRAY_BUFFER_BINDING, GL_VERTEX_ATTRIB_ARRAY_DIVISOR, GL_VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE, GL_VERTEX_ATTRIB_ARRAY_ENABLED, GL_VERTEX_ATTRIB_ARRAY_INTEGER, GL_VERTEX_ATTRIB_ARRAY_NORMALIZED, GL_VERTEX_ATTRIB_ARRAY_POINTER, GL_VERTEX_ATTRIB_ARRAY_SIZE, GL_VERTEX_ATTRIB_ARRAY_STRIDE, GL_VERTEX_ATTRIB_ARRAY_TYPE, GL_VERTEX_SHADER, GL_VIEWPORT, GL_WAIT_FAILED, GL_ZERO };\n", "export const isSSR = typeof window === 'undefined';\n", "import { isSSR } from './ssr';\n\nexport const deviceInfo = (() => {\n  if (isSSR) {\n    return;\n  }\n\n  const { userAgent, platform, maxTouchPoints } = window.navigator;\n\n  const isIOS = /(iphone|ipod|ipad)/i.test(userAgent);\n\n  // Workaround for ipadOS, force detection as tablet\n  // SEE: https://github.com/lancedikson/bowser/issues/329\n  // SEE: https://stackoverflow.com/questions/58019463/how-to-detect-device-name-in-safari-on-ios-13-while-it-doesnt-show-the-correct\n  const isIpad =\n    platform === 'iPad' ||\n    // @ts-expect-error window.MSStream is non standard\n    (platform === 'MacIntel' && maxTouchPoints > 0 && !window.MSStream);\n\n  const isAndroid = /android/i.test(userAgent);\n\n  return {\n    isIpad,\n    isMobile: isAndroid || isIOS || isIpad,\n    isSafari12: /Version\\/12.+Safari/.test(userAgent),\n    isFirefox: /Firefox/.test(userAgent)\n  };\n})();\n", "// Vendor\nimport {\n  G<PERSON>_ARRAY_BUFFER,\n  GL_COLOR_BUFFER_BIT,\n  GL_FLOAT,\n  GL_FRAGMENT_SHADER,\n  GL_RGBA,\n  GL_STATIC_DRAW,\n  GL_TRIANGLES,\n  GL_UNSIGNED_BYTE,\n  GL_VERTEX_SHADER,\n} from 'webgl-constants';\n\n// Internal\nimport { deviceInfo } from './deviceInfo';\n\nconst debug = false ? console.warn : undefined;\n\nexport function deobfuscateAppleGPU(\n  gl: WebGLRenderingContext,\n  renderer: string,\n  isMobileTier: boolean\n) {\n  if (!isMobileTier) {\n    debug?.('Safari 14+ obfuscates its GPU type and version, using fallback');\n    return [renderer];\n  }\n  const pixelId = calculateMagicPixelId(gl);\n  const codeA = '801621810' as const;\n  const codeB = '8016218135' as const;\n  const codeC = '80162181161' as const;\n  const codeFB = '80162181255';\n\n  // All chipsets that support at least iOS 12:\n  const possibleChipsets: [\n    string,\n    typeof codeA | typeof codeB | typeof codeC,\n    number,\n  ][] = deviceInfo?.isIpad\n    ? [\n        // ['a4', 5], // ipad 1st gen\n        // ['a5', 9], // ipad 2 / ipad mini 1st gen\n        // ['a5x', 9], // ipad 3rd gen\n        // ['a6x', 10], // ipad 4th gen\n        ['a7', codeC, 12], // ipad air / ipad mini 2 / ipad mini 3\n        ['a8', codeB, 15], // pad mini 4\n        ['a8x', codeB, 15], // ipad air 2\n        ['a9', codeB, 15], // ipad 5th gen\n        ['a9x', codeB, 15], // pro 9.7 2016 / pro 12.9 2015\n        ['a10', codeB, 15], // ipad 7th gen / ipad 6th gen\n        ['a10x', codeB, 15], // pro 10.5 2017 / pro 12.9 2nd gen, 2017\n        ['a12', codeA, 15], // ipad 8th gen / ipad air 3rd gen / ipad mini 5th gen\n        ['a12x', codeA, 15], // ipad pro 11 3st gen / ipad pro 12.9 3rd gen\n        ['a12z', codeA, 15], // ipad pro 11 4nd gen / ipad pro 12.9 4th gen\n        ['a14', codeA, 15], // ipad air 4th gen\n        ['a15', codeA, 15], // ipad mini 6th gen / ipad 10th gen\n        ['m1', codeA, 15], // ipad pro 11 5nd gen / ipad pro 12.9 5th gen / ipad air 5th gen\n        ['m2', codeA, 15], // ipad pro 11 6nd gen / ipad pro 12.9 6th gen\n      ]\n    : [\n        // ['a4', 7], // 4 / ipod touch 4th gen\n        // ['a5', 9], // 4S / ipod touch 5th gen\n        // ['a6', 10], // 5 / 5C\n        ['a7', codeC, 12], // 5S\n        ['a8', codeB, 12], // 6 / 6 plus / ipod touch 6th gen\n        ['a9', codeB, 15], // 6s / 6s plus/ se 1st gen\n        ['a10', codeB, 15], // 7 / 7 plus / iPod Touch 7th gen\n        ['a11', codeA, 15], // 8 / 8 plus / X\n        ['a12', codeA, 15], // XS / XS Max / XR\n        ['a13', codeA, 15], // 11 / 11 pro / 11 pro max / se 2nd gen\n        ['a14', codeA, 15], // 12 / 12 mini / 12 pro / 12 pro max\n        ['a15', codeA, 15], // 13 / 13 mini / 13 pro / 13 pro max / se 3rd gen / 14 / 14 plus\n        ['a16', codeA, 15], // 14 pro / 14 pro max / 15 / 15 plus\n        ['a17', codeA, 15], // 15 pro / 15 pro max\n      ];\n  let chipsets: typeof possibleChipsets;\n\n  // In iOS 14.x Apple started normalizing the outcome of this hack,\n  // we use this fact to limit the list to devices that support ios 14+\n  if (pixelId === codeFB) {\n    chipsets = possibleChipsets.filter(([, , iosVersion]) => iosVersion >= 14);\n  } else {\n    chipsets = possibleChipsets.filter(([, id]) => id === pixelId);\n    // If nothing was found to match the pixel id, include all chipsets:\n    if (!chipsets.length) {\n      chipsets = possibleChipsets;\n    }\n  }\n  const renderers = chipsets.map(([gpu]) => `apple ${gpu} gpu`);\n  debug?.(\n    `iOS 12.2+ obfuscates its GPU type and version, using closest matches: ${JSON.stringify(\n      renderers\n    )}`\n  );\n  return renderers;\n}\n\n// Apple GPU (iOS 12.2+, Safari 14+)\n// SEE: https://github.com/pmndrs/detect-gpu/issues/7\n// CREDIT: https://medium.com/@Samsy/detecting-apple-a10-iphone-7-to-a11-iphone-8-and-b019b8f0eb87\n// CREDIT: https://github.com/Samsy/appleGPUDetection/blob/master/index.js\nfunction calculateMagicPixelId(gl: WebGLRenderingContext) {\n  const vertexShaderSource = /* glsl */ `\n    precision highp float;\n    attribute vec3 aPosition;\n    varying float vvv;\n    void main() {\n      vvv = 0.31622776601683794;\n      gl_Position = vec4(aPosition, 1.0);\n    }\n  `;\n\n  const fragmentShaderSource = /* glsl */ `\n    precision highp float;\n    varying float vvv;\n    void main() {\n      vec4 enc = vec4(1.0, 255.0, 65025.0, 16581375.0) * vvv;\n      enc = fract(enc);\n      enc -= enc.yzww * vec4(1.0 / 255.0, 1.0 / 255.0, 1.0 / 255.0, 0.0);\n      gl_FragColor = enc;\n    }\n  `;\n\n  const vertexShader = gl.createShader(GL_VERTEX_SHADER);\n  const fragmentShader = gl.createShader(GL_FRAGMENT_SHADER);\n  const program = gl.createProgram();\n  if (!(fragmentShader && vertexShader && program)) return;\n  gl.shaderSource(vertexShader, vertexShaderSource);\n  gl.shaderSource(fragmentShader, fragmentShaderSource);\n  gl.compileShader(vertexShader);\n  gl.compileShader(fragmentShader);\n  gl.attachShader(program, vertexShader);\n  gl.attachShader(program, fragmentShader);\n\n  gl.linkProgram(program);\n\n  gl.detachShader(program, vertexShader);\n  gl.detachShader(program, fragmentShader);\n  gl.deleteShader(vertexShader);\n  gl.deleteShader(fragmentShader);\n\n  gl.useProgram(program);\n\n  const vertexArray = gl.createBuffer();\n  gl.bindBuffer(GL_ARRAY_BUFFER, vertexArray);\n  gl.bufferData(\n    GL_ARRAY_BUFFER,\n    new Float32Array([-1, -1, 0, 3, -1, 0, -1, 3, 0]),\n    GL_STATIC_DRAW\n  );\n\n  const aPosition = gl.getAttribLocation(program, 'aPosition');\n  gl.vertexAttribPointer(aPosition, 3, GL_FLOAT, false, 0, 0);\n  gl.enableVertexAttribArray(aPosition);\n\n  gl.clearColor(1, 1, 1, 1);\n  gl.clear(GL_COLOR_BUFFER_BIT);\n  gl.viewport(0, 0, 1, 1);\n  gl.drawArrays(GL_TRIANGLES, 0, 3);\n\n  const pixels = new Uint8Array(4);\n  gl.readPixels(0, 0, 1, 1, GL_RGBA, GL_UNSIGNED_BYTE, pixels);\n\n  gl.deleteProgram(program);\n  gl.deleteBuffer(vertexArray);\n  return pixels.join('');\n}\n", "export class OutdatedBenchmarksError extends Error {\n  constructor(message?: string) {\n    super(message); // 'Error' breaks prototype chain here\n    Object.setPrototypeOf(this, new.target.prototype); // restore prototype chain\n  }\n}\n", "// Caches\nconst array: number[] = [];\nconst charCodeCache: number[] = [];\n\n// Compute the difference (distance) between two strings\n// SEE: https://en.wikipedia.org/wiki/Levenshtein_distance\n// CREDIT: https://github.com/sindresorhus/leven (version 3.1.0)\nexport function getLevenshteinDistance(left: string, right: string): number {\n  if (left === right) {\n    return 0;\n  }\n\n  const swap = left;\n\n  // Swapping the strings if `a` is longer than `b` so we know which one is the\n  // shortest & which one is the longest\n  if (left.length > right.length) {\n    left = right;\n    right = swap;\n  }\n\n  let leftLength = left.length;\n  let rightLength = right.length;\n\n  // Performing suffix trimming:\n  // We can linearly drop suffix common to both strings since they\n  // don't increase distance at all\n  // Note: `~-` is the bitwise way to perform a `- 1` operation\n  while (leftLength > 0 &&\n    left.charCodeAt(~-leftLength) === right.charCodeAt(~-rightLength)) {\n    leftLength--;\n    rightLength--;\n  }\n\n  // Performing prefix trimming\n  // We can linearly drop prefix common to both strings since they\n  // don't increase distance at all\n  let start = 0;\n\n  while (start < leftLength &&\n    left.charCodeAt(start) === right.charCodeAt(start)) {\n    start++;\n  }\n\n  leftLength -= start;\n  rightLength -= start;\n\n  if (leftLength === 0) {\n    return rightLength;\n  }\n\n  let bCharCode;\n  let result = 0;\n  let temp;\n  let temp2;\n  let i = 0;\n  let j = 0;\n\n  while (i < leftLength) {\n    charCodeCache[i] = left.charCodeAt(start + i);\n    array[i] = ++i;\n  }\n\n  while (j < rightLength) {\n    bCharCode = right.charCodeAt(start + j);\n    temp = j++;\n    result = j;\n\n    for (i = 0; i < leftLength; i++) {\n      temp2 = bCharCode === charCodeCache[i] ? temp : temp + 1;\n      temp = array[i];\n      // eslint-disable-next-line no-multi-assign\n      result = array[i] =\n        temp > result\n          ? temp2 > result\n            ? result + 1\n            : temp2\n          : temp2 > temp\n            ? temp + 1\n            : temp2;\n    }\n  }\n\n  return result;\n}\n\nexport function tokenizeForLevenshteinDistance(str: string): string {\n  return str\n    .split(/[.,()\\[\\]/\\s]/g)\n    .sort()\n    // Remove duplicates\n    .filter((item, pos, arr) => pos === 0 || item !== arr[pos - 1])\n    .join(' ');\n}\n", "export function isDefined<T>(val: T | undefined | null | void): val is T {\n  return val !== undefined && val !== null;\n}\n", "// Data\nimport { version } from '../package.json';\n\n// Internal\nimport { BLOCKLISTED_GPUS } from './internal/blocklistedGPUS';\nimport { cleanRenderer } from './internal/cleanRenderer';\nimport { deobfuscateRenderer } from './internal/deobfuscateRenderer';\nimport { deviceInfo } from './internal/deviceInfo';\nimport { OutdatedBenchmarksError } from './internal/error';\nimport { getGPUVersion } from './internal/getGPUVersion';\nimport {\n  getLevenshteinDistance,\n  tokenizeForLevenshteinDistance,\n} from './internal/getLevenshteinDistance';\nimport { getWebGLContext } from './internal/getWebGLContext';\nimport { isSSR } from './internal/ssr';\nimport { isDefined } from './internal/util';\n\n// Types\nexport interface GetGPUTier {\n  /**\n   * URL of directory where benchmark data is hosted.\n   *\n   * @default https://unpkg.com/detect-gpu@{version}/dist/benchmarks\n   */\n  benchmarksURL?: string;\n  /**\n   * Optionally pass in a WebGL context to avoid creating a temporary one\n   * internally.\n   */\n  glContext?: WebGLRenderingContext | WebGL2RenderingContext;\n  /**\n   * Whether to fail if the system performance is low or if no hardware GPU is\n   * available.\n   *\n   * @default false\n   */\n  failIfMajorPerformanceCaveat?: boolean;\n  /**\n   * Framerate per tier for mobile devices.\n   *\n   * @defaultValue [0, 15, 30, 60]\n   */\n  mobileTiers?: number[];\n  /**\n   * Framerate per tier for desktop devices.\n   *\n   * @defaultValue [0, 15, 30, 60]\n   */\n  desktopTiers?: number[];\n  /**\n   * Optionally override specific parameters. Used mainly for testing.\n   */\n  override?: {\n    renderer?: string;\n    /**\n     * Override whether device is an iPad.\n     */\n    isIpad?: boolean;\n    /**\n     * Override whether device is a mobile device.\n     */\n    isMobile?: boolean;\n    /**\n     * Override device screen size.\n     */\n    screenSize?: { width: number; height: number };\n    /**\n     * Override how benchmark data is loaded\n     */\n    loadBenchmarks?: (file: string) => Promise<ModelEntry[]>;\n  };\n}\n\nexport type TierType =\n  | 'SSR'\n  | 'WEBGL_UNSUPPORTED'\n  | 'BLOCKLISTED'\n  | 'FALLBACK'\n  | 'BENCHMARK';\n\nexport type TierResult = {\n  tier: number;\n  type: TierType;\n  isMobile?: boolean;\n  fps?: number;\n  gpu?: string;\n  device?: string;\n};\n\nexport type ModelEntryScreen = [number, number, number, string | undefined];\n\nexport type ModelEntry = [string, string, string, 0 | 1, ModelEntryScreen[]];\n\nconst debug = false ? console.log : undefined;\n\nexport const getGPUTier = async ({\n  mobileTiers = [0, 15, 30, 60],\n  desktopTiers = [0, 15, 30, 60],\n  override = {},\n  glContext,\n  failIfMajorPerformanceCaveat = false,\n  benchmarksURL = `https://unpkg.com/detect-gpu@${version}/dist/benchmarks`,\n}: GetGPUTier = {}): Promise<TierResult> => {\n  const queryCache: { [k: string]: Promise<ModelEntry[]> } = {};\n  if (isSSR) {\n    return {\n      tier: 0,\n      type: 'SSR',\n    };\n  }\n\n  const {\n    isIpad = !!deviceInfo?.isIpad,\n    isMobile = !!deviceInfo?.isMobile,\n    screenSize = window.screen,\n    loadBenchmarks = async (file: string) => {\n      const data: ModelEntry[] = await fetch(`${benchmarksURL}/${file}`).then(\n        (response) => response.json()\n      );\n\n      // Remove version tag and check version is supported\n      const version = parseInt(\n        (data.shift() as unknown as string).split('.')[0],\n        10\n      );\n      if (version < 4) {\n        throw new OutdatedBenchmarksError(\n          'Detect GPU benchmark data is out of date. Please update to version 4x'\n        );\n      }\n      return data;\n    },\n  } = override;\n  let { renderer } = override;\n  const getGpuType = (renderer: string) => {\n    const types = isMobile\n      ? ([\n          'adreno',\n          'apple',\n          'mali-t',\n          'mali',\n          'nvidia',\n          'powervr',\n          'samsung',\n        ] as const)\n      : ([\n          'intel',\n          'apple',\n          'amd',\n          'radeon',\n          'nvidia',\n          'geforce',\n          'adreno',\n        ] as const);\n    for (const type of types) {\n      if (renderer.includes(type)) {\n        return type;\n      }\n    }\n  };\n\n  async function queryBenchmarks(renderer: string) {\n    const type = getGpuType(renderer);\n    if (!type) {\n      return;\n    }\n\n    debug?.('queryBenchmarks - found type:', { type });\n\n    const benchmarkFile = `${isMobile ? 'm' : 'd'}-${type}${\n      isIpad ? '-ipad' : ''\n    }.json`;\n\n    const benchmark = (queryCache[benchmarkFile] =\n      queryCache[benchmarkFile] ?? loadBenchmarks(benchmarkFile));\n    let benchmarks: ModelEntry[];\n    try {\n      benchmarks = await benchmark;\n    } catch (error) {\n      if (error instanceof OutdatedBenchmarksError) {\n        throw error;\n      }\n      debug?.(\"queryBenchmarks - couldn't load benchmark:\", { error });\n      return;\n    }\n\n    const version = getGPUVersion(renderer);\n\n    let matched = benchmarks.filter(\n      ([, modelVersion]) => modelVersion === version\n    );\n\n    debug?.(\n      `found ${matched.length} matching entries using version '${version}':`,\n\n      matched.map(([model]) => model)\n    );\n\n    // If nothing matched, try comparing model names:\n    if (!matched.length) {\n      matched = benchmarks.filter(([model]) => model.includes(renderer));\n\n      debug?.(\n        `found ${matched.length} matching entries comparing model names`,\n        {\n          matched,\n        }\n      );\n    }\n\n    const matchCount = matched.length;\n\n    if (matchCount === 0) {\n      return;\n    }\n\n    const tokenizedRenderer = tokenizeForLevenshteinDistance(renderer);\n    // eslint-disable-next-line prefer-const\n    let [gpu, , , , fpsesByPixelCount] =\n      matchCount > 1\n        ? matched\n            .map(\n              (match) =>\n                [\n                  match,\n                  getLevenshteinDistance(tokenizedRenderer, match[2]),\n                ] as const\n            )\n            .sort(([, a], [, b]) => a - b)[0][0]\n        : matched[0];\n\n    debug?.(\n      `${renderer} matched closest to ${gpu} with the following screen sizes`,\n      JSON.stringify(fpsesByPixelCount)\n    );\n\n    let minDistance = Number.MAX_VALUE;\n    let closest: ModelEntryScreen | undefined;\n    const { devicePixelRatio } = window;\n    const pixelCount =\n      screenSize.width *\n      devicePixelRatio *\n      screenSize.height *\n      devicePixelRatio;\n\n    for (const match of fpsesByPixelCount) {\n      const [width, height] = match;\n      const entryPixelCount = width * height;\n      const distance = Math.abs(pixelCount - entryPixelCount);\n\n      if (distance < minDistance) {\n        minDistance = distance;\n        closest = match;\n      }\n    }\n\n    if (!closest) {\n      return;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const [, , fps, device] = closest!;\n\n    return [minDistance, fps, gpu, device] as const;\n  }\n\n  const toResult = (\n    tier: number,\n    type: TierType,\n    gpu?: string,\n    fps?: number,\n    device?: string\n  ) => ({\n    device,\n    fps,\n    gpu,\n    isMobile,\n    tier,\n    type,\n  });\n\n  let renderers: string[];\n  let rawRenderer = '';\n\n  if (!renderer) {\n    const gl =\n      glContext ||\n      getWebGLContext(deviceInfo?.isSafari12, failIfMajorPerformanceCaveat);\n\n    if (!gl) {\n      return toResult(0, 'WEBGL_UNSUPPORTED');\n    }\n\n    const debugRendererInfo = deviceInfo?.isFirefox\n      ? null\n      : gl.getExtension('WEBGL_debug_renderer_info');\n\n    renderer = debugRendererInfo\n      ? gl.getParameter(debugRendererInfo.UNMASKED_RENDERER_WEBGL)\n      : gl.getParameter(gl.RENDERER);\n\n    if (!renderer) {\n      return toResult(1, 'FALLBACK');\n    }\n\n    rawRenderer = renderer;\n    renderer = cleanRenderer(renderer);\n    renderers = deobfuscateRenderer(gl, renderer, isMobile);\n  } else {\n    renderer = cleanRenderer(renderer);\n    renderers = [renderer];\n  }\n\n  const results = (await Promise.all(renderers.map(queryBenchmarks)))\n    .filter(isDefined)\n    .sort(([aDis = Number.MAX_VALUE, aFps], [bDis = Number.MAX_VALUE, bFps]) =>\n      aDis === bDis ? aFps - bFps : aDis - bDis\n    );\n  if (!results.length) {\n    const blocklistedModel: string | undefined = BLOCKLISTED_GPUS.find(\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      (blocklistedModel) => renderer!.includes(blocklistedModel)\n    );\n    return blocklistedModel\n      ? toResult(0, 'BLOCKLISTED', blocklistedModel)\n      : toResult(1, 'FALLBACK', `${renderer} (${rawRenderer})`);\n  }\n\n  const [, fps, model, device] = results[0];\n\n  if (fps === -1) {\n    return toResult(0, 'BLOCKLISTED', model, fps, device);\n  }\n\n  const tiers = isMobile ? mobileTiers : desktopTiers;\n  let tier = 0;\n\n  for (let i = 0; i < tiers.length; i++) {\n    if (fps >= tiers[i]) {\n      tier = i;\n    }\n  }\n\n  return toResult(tier, 'BENCHMARK', model, fps, device);\n};\n", "export function getGPUVersion(model: string) {\n  model = model.replace(/\\([^)]+\\)/, '');\n\n  const matches =\n    // First set of digits\n    model.match(/\\d+/) ||\n    // If the renderer did not contain any numbers, match letters\n    model.match(/(\\W|^)([A-Za-z]{1,3})(\\W|$)/g);\n\n  // Remove any non-word characters and also remove 'amd' which could be matched\n  // in the clause above\n  return matches?.join('').replace(/\\W|amd/g, '') ?? '';\n}\n", "export function getWebGLContext(isSafari12?: boolean,\n  failIfMajorPerformanceCaveat = false) {\n  const attributes: {\n    alpha: boolean;\n    antialias: boolean;\n    depth: boolean;\n    failIfMajorPerformanceCaveat: boolean;\n    powerPreference?: string;\n    stencil: boolean;\n  } = {\n    alpha: false,\n    antialias: false,\n    depth: false,\n    failIfMajorPerformanceCaveat,\n    powerPreference: 'high-performance',\n    stencil: false,\n  };\n\n  // Workaround for Safari 12, which otherwise crashes with powerPreference set\n  // to high-performance: https://github.com/pmndrs/detect-gpu/issues/5\n  if (isSafari12) {\n    delete attributes.powerPreference;\n  }\n\n  const canvas = window.document.createElement('canvas');\n\n  const gl = (canvas.getContext('webgl', attributes) ||\n    canvas.getContext(\n      'experimental-webgl',\n      attributes\n    )) as WebGLRenderingContext | null;\n\n  return gl ?? undefined;\n}\n", "// Internal\nimport { deobfuscateAppleGPU } from './deobfuscateAppleGPU';\n\nexport function deobfuscateRenderer(\n  gl: WebGLRenderingContext | WebGL2RenderingContext,\n  renderer: string,\n  isMobileTier: boolean\n) {\n  return renderer === 'apple gpu'\n    ? deobfuscateAppleGPU(gl, renderer, isMobileTier)\n    : [renderer];\n}\n"], "names": ["BLOCKLISTED_GPUS", "<PERSON><PERSON><PERSON><PERSON>", "renderer", "toLowerCase", "replace", "GL_ARRAY_BUFFER", "isSSR", "window", "deviceInfo", "_a", "navigator", "userAgent", "platform", "maxTouchPoints", "isIOS", "test", "isIpad", "MSStream", "isMobile", "isSafari12", "isFirefox", "deobfuscateAppleGPU", "gl", "isMobileTier", "chipsets", "pixelId", "vertexShaderSource", "fragmentShaderSource", "vertexShader", "createShader", "fragmentShader", "program", "createProgram", "shaderSource", "compileShader", "<PERSON><PERSON><PERSON><PERSON>", "linkProgram", "deta<PERSON><PERSON><PERSON><PERSON>", "deleteShader", "useProgram", "vertexArray", "createBuffer", "<PERSON><PERSON><PERSON><PERSON>", "bufferData", "Float32Array", "aPosition", "getAttribLocation", "vertexAttribPointer", "enableVertexAttribArray", "clearColor", "clear", "viewport", "drawArrays", "pixels", "Uint8Array", "readPixels", "deleteProgram", "deleteBuffer", "join", "calculateMagicPixelId", "codeA", "codeB", "codeC", "possibleChipsets", "filter", "length", "map", "gpu", "concat", "OutdatedBenchmarksError", "_super", "message", "_this", "call", "this", "Object", "setPrototypeOf", "_newTarget", "prototype", "__extends", "Error", "array", "char<PERSON><PERSON><PERSON><PERSON>", "getLevenshteinDistance", "left", "right", "swap", "left<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "charCodeAt", "bCharCode", "start", "temp", "temp2", "result", "i", "j", "isDefined", "val", "_b", "_c", "mobileTiers", "_d", "desktopTiers", "_e", "override", "glContext", "_f", "failIfMajorPerformanceCaveat", "_g", "benchmarksURL", "queryBenchmarks", "type", "getGpuType", "benchmarkFile", "benchmark", "queryCache", "loadBenchmarks", "benchmarks", "error_1", "version", "model", "matches", "match", "getGPUVersion", "matched", "includes", "matchCount", "tokenizedRenderer", "split", "sort", "item", "pos", "arr", "fpsesByPixelCount", "minDistance", "Number", "MAX_VALUE", "devicePixelRatio", "pixelCount", "screenSize", "width", "height", "_i", "fpsesByPixelCount_1", "entryPixelCount", "distance", "Math", "abs", "closest", "fps", "device", "tier", "_h", "_j", "_k", "screen", "_l", "file", "__awaiter", "fetch", "then", "response", "json", "data", "sent", "parseInt", "shift", "types_1", "toResult", "<PERSON><PERSON><PERSON><PERSON>", "renderers", "attributes", "alpha", "antialias", "depth", "powerPreference", "stencil", "canvas", "document", "createElement", "getContext", "undefined", "getWebGLContext", "debugRendererInfo", "getExtension", "getParameter", "UNMASKED_RENDERER_WEBGL", "RENDERER", "deobfuscate<PERSON><PERSON><PERSON>", "Promise", "all", "results", "_o", "aDis", "aFps", "bDis", "bFps", "blocklistedModel", "find", "_m", "tiers"], "mappings": "u1DAGaA,EAAmB,CAC9B,eACA,eACA,mBACA,kBACA,kBACA,eACA,kBACA,gBACA,mBACA,gBACA,oBACA,iBACA,iBACA,kBACA,kBACA,qBACA,YACA,YACA,oBACA,kBACA,gBACA,YACA,SACA,SACA,SACA,SACA,iBACA,cACA,cACA,iBACA,iBACA,iBACA,iBACA,iBACA,iBACA,iBACA,iBACA,iBACA,iBACA,iBACA,kBACA,kBACA,kBACA,kBACA,kBACA,UACA,UChDI,SAAUC,EAAcC,GAsB5B,OAnBAA,EAAWA,EACRC,cAIAC,QAAQ,6CAA8C,MAItDA,QAAQ,+CAAgD,IAMxDA,QAAQ,qDAAsD,MCUnE,MAkbMC,EAAkB,MChdjB,IAAMC,EAA0B,oBAAXC,OCEfC,EAAa,WACxB,IAAIF,EAAJ,CAIM,IAAAG,EAA0CF,OAAOG,UAA/CC,EAASF,EAAAE,UAAEC,EAAQH,EAAAG,SAAEC,mBAEvBC,EAAQ,sBAAsBC,KAAKJ,GAKnCK,EACS,SAAbJ,GAEc,aAAbA,GAA2BC,EAAiB,IAAMN,OAAOU,SAI5D,MAAO,CACLD,OAAMA,EACNE,SAJgB,WAAWH,KAAKJ,IAITG,GAASE,EAChCG,WAAY,sBAAsBJ,KAAKJ,GACvCS,UAAW,UAAUL,KAAKJ,KAvBJ,YCgBVU,EACdC,EACApB,EACAqB,GAEA,IAAKA,EAEH,MAAO,CAACrB,GAEV,IAgDIsB,EAhDEC,EA0ER,SAA+BH,GAC7B,IAAMI,EAAgC,iMAUhCC,EAAkC,mRAWlCC,EAAeN,EAAGO,aHwlBD,OGvlBjBC,EAAiBR,EAAGO,aHklBD,OGjlBnBE,EAAUT,EAAGU,gBACnB,KAAMF,GAAkBF,GAAgBG,GAAU,OAClDT,EAAGW,aAAaL,EAAcF,GAC9BJ,EAAGW,aAAaH,EAAgBH,GAChCL,EAAGY,cAAcN,GACjBN,EAAGY,cAAcJ,GACjBR,EAAGa,aAAaJ,EAASH,GACzBN,EAAGa,aAAaJ,EAASD,GAEzBR,EAAGc,YAAYL,GAEfT,EAAGe,aAAaN,EAASH,GACzBN,EAAGe,aAAaN,EAASD,GACzBR,EAAGgB,aAAaV,GAChBN,EAAGgB,aAAaR,GAEhBR,EAAGiB,WAAWR,GAEd,IAAMS,EAAclB,EAAGmB,eACvBnB,EAAGoB,WAAWrC,EAAiBmC,GAC/BlB,EAAGqB,WACDtC,EACA,IAAIuC,aAAa,EAAE,GAAI,EAAG,EAAG,GAAI,EAAG,GAAI,EAAG,EAAG,IH8S3B,OG1SrB,IAAMC,EAAYvB,EAAGwB,kBAAkBf,EAAS,aAChDT,EAAGyB,oBAAoBF,EAAW,EHygBnB,MGzgBgC,EAAO,EAAG,GACzDvB,EAAG0B,wBAAwBH,GAE3BvB,EAAG2B,WAAW,EAAG,EAAG,EAAG,GACvB3B,EAAG4B,MH9HuB,OG+H1B5B,EAAG6B,SAAS,EAAG,EAAG,EAAG,GACrB7B,EAAG8B,WHrGgB,EGqGS,EAAG,GAE/B,IAAMC,EAAS,IAAIC,WAAW,GAK9B,OAJAhC,EAAGiC,WAAW,EAAG,EAAG,EAAG,EHihBT,KArCS,KG5e8BF,GAErD/B,EAAGkC,cAAczB,GACjBT,EAAGmC,aAAajB,GACTa,EAAOK,KAAK,IA1IHC,CAAsBrC,GAChCsC,EAAQ,YACRC,EAAQ,aACRC,EAAQ,cAIRC,GAIAvD,MAAAA,OAAU,EAAVA,EAAYQ,QACd,CAKE,CAAC,KAAM8C,EAAO,IACd,CAAC,KAAMD,EAAO,IACd,CAAC,MAAOA,EAAO,IACf,CAAC,KAAMA,EAAO,IACd,CAAC,MAAOA,EAAO,IACf,CAAC,MAAOA,EAAO,IACf,CAAC,OAAQA,EAAO,IAChB,CAAC,MAAOD,EAAO,IACf,CAAC,OAAQA,EAAO,IAChB,CAAC,OAAQA,EAAO,IAChB,CAAC,MAAOA,EAAO,IACf,CAAC,MAAOA,EAAO,IACf,CAAC,KAAMA,EAAO,IACd,CAAC,KAAMA,EAAO,KAEhB,CAIE,CAAC,KAAME,EAAO,IACd,CAAC,KAAMD,EAAO,IACd,CAAC,KAAMA,EAAO,IACd,CAAC,MAAOA,EAAO,IACf,CAAC,MAAOD,EAAO,IACf,CAAC,MAAOA,EAAO,IACf,CAAC,MAAOA,EAAO,IACf,CAAC,MAAOA,EAAO,IACf,CAAC,MAAOA,EAAO,IACf,CAAC,MAAOA,EAAO,IACf,CAAC,MAAOA,EAAO,KAqBrB,MA/De,gBAgDXnC,EACFD,EAAWuC,EAAiBC,QAAO,SAACvD,GAAqB,OAANA,EAAA,IAAoB,OAEvEe,EAAWuC,EAAiBC,QAAO,SAACvD,GAAW,OAANA,EAAA,KAAagB,MAExCwC,SACZzC,EAAWuC,GAGGvC,EAAS0C,KAAI,SAACzD,GAAC,IAAA0D,EAAG1D,EAAA,GAAM,MAAA,SAAA2D,OAASD,EAAS,WCxF9D,IAAAE,EAAA,SAAAC,GACE,SAAAD,EAAYE,0BACVC,EAAAF,EAAAG,KAAAC,KAAMH,IAEPG,YADCC,OAAOC,eAAeJ,EAAMK,EAAWC,aAE3C,0PAL6CC,CAAKV,EAAAC,GAKjDD,EALD,CAA6CW,OCC7C,IAAMC,EAAkB,GAClBC,EAA0B,GAKhB,SAAAC,EAAuBC,EAAcC,GACnD,GAAID,IAASC,EACX,OAAO,EAGT,IAAMC,EAAOF,EAITA,EAAKnB,OAASoB,EAAMpB,SACtBmB,EAAOC,EACPA,EAAQC,GAUV,IAPA,IAAIC,EAAaH,EAAKnB,OAClBuB,EAAcH,EAAMpB,OAMjBsB,EAAa,GAClBH,EAAKK,aAAaF,KAAgBF,EAAMI,aAAaD,IACrDD,IACAC,IAQF,IAFA,IAcIE,EAdAC,EAAQ,EAELA,EAAQJ,GACbH,EAAKK,WAAWE,KAAWN,EAAMI,WAAWE,IAC5CA,IAMF,GAFAH,GAAeG,EAEI,KAHnBJ,GAAcI,GAIZ,OAAOH,EAUT,IANA,IACII,EACAC,EAFAC,EAAS,EAGTC,EAAI,EACJC,EAAI,EAEDD,EAAIR,GACTL,EAAca,GAAKX,EAAKK,WAAWE,EAAQI,GAC3Cd,EAAMc,KAAOA,EAGf,KAAOC,EAAIR,GAKT,IAJAE,EAAYL,EAAMI,WAAWE,EAAQK,GACrCJ,EAAOI,IACPF,EAASE,EAEJD,EAAI,EAAGA,EAAIR,EAAYQ,IAC1BF,EAAQH,IAAcR,EAAca,GAAKH,EAAOA,EAAO,EACvDA,EAAOX,EAAMc,GAEbD,EAASb,EAAMc,GACbH,EAAOE,EACHD,EAAQC,EACNA,EAAS,EACTD,EACFA,EAAQD,EACNA,EAAO,EACPC,EAIZ,OAAOC,ECnFH,SAAUG,EAAaC,GAC3B,OAAOA,MAAAA,eC+FiB,SAAOzF,OAAA0F,OAOjB,IAAA1F,EAAA,GAAEA,EANhB2F,EAA6BD,EAAAE,YAA7BA,OAAW,IAAAD,EAAG,CAAC,EAAG,GAAI,GAAI,IAAGA,EAC7BE,EAA8BH,EAAAI,aAA9BA,OAAY,IAAAD,EAAG,CAAC,EAAG,GAAI,GAAI,IAAGA,EAC9BE,EAAaL,EAAAM,SAAbA,OAAQ,IAAAD,EAAG,GAAEA,EACbE,EAASP,EAAAO,UACTC,EAAoCR,EAAAS,6BAApCA,OAA4B,IAAAD,GAAQA,EACpCE,EAAAV,EAAAW,cAAAA,OAAgB,IAAAD,EAAA,gCAAAzC,gBAAuC,oBAAkByC,4CA4DzE,SAAeE,EAAgB7G,2JAE7B,KADM8G,EAAOC,EAAW/G,IAEtB,MAAO,CAAA,GAKHgH,EAAgB,GAAG9C,OAAAlD,EAAW,IAAM,IAAG,KAAAkD,OAAI4C,GAC/C5C,OAAApD,EAAS,QAAU,GAAE,SAGjBmG,EAAaC,EAAWF,GACH,QAAzBzG,EAAA2G,EAAWF,UAAc,IAAAzG,EAAAA,EAAI4G,EAAeH,oBAG/B,6BAAA,CAAA,EAAMC,iBAAnBG,EAAahB,sBAEb,0BAAqBjC,EACnB,MAAMkD,EAGR,MAAO,CAAA,UA6BT,GA1BMC,EC3LJ,SAAwBC,SAGtBC,GAFND,EAAQA,EAAMrH,QAAQ,YAAa,KAI3BuH,MAAM,QAEZF,EAAME,MAAM,gCAId,OAA+C,UAAxCD,MAAAA,OAAA,EAAAA,EAAShE,KAAK,IAAItD,QAAQ,UAAW,WAAG,IAAAK,EAAAA,EAAI,GDgLjCmH,CAAc1H,IAE1B2H,EAAUP,EAAWtD,QACvB,SAACvD,GAAqB,OAANA,EAAA,KAAuB+G,MAU5BvD,SACX4D,EAAUP,EAAWtD,QAAO,SAACvD,GAAY,OAANA,EAAA,GAAYqH,SAAS5H,OAYvC,KAFb6H,EAAaF,EAAQ5D,QAGzB,MAAO,CAAA,GAgCT,IA7BM+D,EAAmD9H,EFjIxD+H,MAAM,kBACNC,OAEAlE,QAAO,SAACmE,EAAMC,EAAKC,GAAQ,OAAQ,IAARD,GAAaD,IAASE,EAAID,EAAM,MAC3D1E,KAAK,KE+HFyC,EACF4B,EAAa,EACTF,EACG3D,KACC,SAACyD,GACC,MAAA,CACEA,EACAxC,EAAuB6C,EAAmBL,EAAM,QAGrDO,MAAK,SAACzH,EAAO0F,GAAU,OAAb1F,EAAA,GAAO0F,EAAA,MAAa,GAAG,GACpC0B,EAAQ,GAXT1D,EAAGgC,EAAA,GAAQmC,EAAiBnC,EAAA,GAkB7BoC,EAAcC,OAAOC,UAEjBC,EAAqBnI,OAAMmI,iBAC7BC,EACJC,EAAWC,MACXH,EACAE,EAAWE,OACXJ,EAEFK,EAAA,EAAoBC,EAAiBV,EAAjBS,EAAiBC,EAAA/E,OAAjB8E,IAATpB,EAAKqB,EAAAD,GACPF,EAAiBlB,EAAZ,GAAEmB,EAAUnB,KAClBsB,EAAkBJ,EAAQC,GAC1BI,EAAWC,KAAKC,IAAIT,EAAaM,IAExBV,IACbA,EAAcW,EACdG,EAAU1B,GAId,OAAK0B,GAKMC,GAALlD,EAAoBiD,MAAVE,OAET,CAAA,EAAA,CAAChB,EAAae,EAAKnF,EAAKoF,KANtB,CAAA,kHAzJX,GADMnC,EAAqD,GACvD9G,EACF,MAAO,CAAA,EAAA,CACLkJ,KAAM,EACNxC,KAAM,QAiLV,GA5KEyC,EAoBEhD,EApB2BzF,OAA7BA,OAAM,IAAAyI,KAAKjJ,MAAAA,OAAU,EAAVA,EAAYQ,QAAMyI,EAC7BC,EAmBEjD,EAAQvF,SAnBVA,OAAW,IAAAwI,KAAElJ,MAAAA,OAAA,EAAAA,EAAYU,UAAQwI,EACjCC,EAkBElD,EAAQmC,WAlBVA,aAAarI,OAAOqJ,OAAMD,EAC1BE,EAiBEpD,EAAQY,eAjBVA,OAAiB,IAAAwC,EAAA,SAAOC,GAAY,OAAAC,OAAA,OAAA,OAAA,GAAA,mEACP,MAAM,CAAA,EAAAC,MAAM,GAAG5F,OAAA0C,cAAiBgD,IAAQG,MACjE,SAACC,GAAa,OAAAA,EAASC,kBAQzB,GATMC,EAAqB3J,EAE1B4J,OAGeC,SACbF,EAAKG,QAA8BtC,MAAM,KAAK,GAC/C,IAEY,EACZ,MAAM,IAAI5D,EACR,yEAGJ,MAAA,CAAA,EAAO+F,WACRP,EAEG3J,EAAauG,EAAQvG,SACrB+G,EAAa,SAAC/G,GAoBlB,IAnBA,QAmBmBsK,EAnBLtJ,EACT,CACC,SACA,QACA,SACA,OACA,SACA,UACA,WAED,CACC,QACA,QACA,MACA,SACA,SACA,UACA,UAEa6H,EAAKyB,EAAAvG,OAAL8E,IAAO,CAArB,IAAM/B,EAAIwD,EAAAzB,GACb,GAAI7I,EAAS4H,SAASd,GACpB,OAAOA,IA8GPyD,EAAW,SACfjB,EACAxC,EACA7C,EACAmF,EACAC,GACG,MAAC,CACJA,OAAMA,EACND,IAAGA,EACHnF,IAAGA,EACHjD,SAAQA,EACRsI,KAAIA,EACJxC,KAAIA,IAIF0D,EAAc,GAEbxK,EAyBHA,EAAWD,EAAcC,GACzByK,EAAY,CAACzK,OA1BA,CAKb,KAJMoB,EACJoF,GE/RU,SAAgBvF,EAC9ByF,QAAA,IAAAA,IAAAA,GAAoC,GACpC,IAAMgE,EAOF,CACFC,OAAO,EACPC,WAAW,EACXC,OAAO,EACPnE,6BAA4BA,EAC5BoE,gBAAiB,mBACjBC,SAAS,GAKP9J,UACKyJ,EAAWI,gBAGpB,IAAME,EAAS3K,OAAO4K,SAASC,cAAc,UAEvC9J,EAAM4J,EAAOG,WAAW,QAAST,IACrCM,EAAOG,WACL,qBACAT,GAGJ,OAAOtJ,MAAAA,EAAAA,OAAMgK,EFgQTC,CAAgB/K,MAAAA,OAAU,EAAVA,EAAYW,WAAYyF,IAGxC,MAAA,CAAA,EAAO6D,EAAS,EAAG,sBAWrB,GARMe,GAAoBhL,MAAAA,OAAA,EAAAA,EAAYY,WAClC,KACAE,EAAGmK,aAAa,+BAEpBvL,EAAWsL,EACPlK,EAAGoK,aAAaF,EAAkBG,yBAClCrK,EAAGoK,aAAapK,EAAGsK,WAGrB,MAAA,CAAA,EAAOnB,EAAS,EAAG,aAGrBC,EAAcxK,EACdA,EAAWD,EAAcC,GACzByK,WGhTFrJ,EACApB,EACAqB,GAEA,MAAoB,cAAbrB,EACHmB,EAAoBC,EAAIpB,EAAUqB,GAClC,CAACrB,GH0SS2L,CAAoBvK,EAAIpB,EAAUgB,GAM/B,MAAM,CAAA,EAAA4K,QAAQC,IAAIpB,EAAUzG,IAAI6C,YAKjD,KALMiF,EAAWC,EAAA5B,OACdrG,OAAOiC,GACPiC,MAAK,SAACzH,EAAiC0F,OAAhCC,EAAuB3F,EAAA,GAAvByL,OAAO,IAAA9F,EAAAoC,OAAOC,UAASrC,EAAE+F,EAAI1L,EAAA,GAAI6F,EAAuBH,EAAA,GAAvBiG,OAAO,IAAA9F,EAAAkC,OAAOC,UAASnC,EAAE+F,EAAIlG,EAAA,GACpE,OAAA+F,IAASE,EAAOD,EAAOE,EAAOH,EAAOE,MAE5BnI,OAKX,MAAA,CAAA,GAJMqI,EAAuCtM,EAAiBuM,MAE5D,SAACD,GAAqB,OAAApM,EAAU4H,SAASwE,OAGvC7B,EAAS,EAAG,cAAe6B,GAC3B7B,EAAS,EAAG,WAAY,GAAArG,OAAGlE,EAAQ,MAAAkE,OAAKsG,EAAW,OAKzD,GAFM8B,EAAyBR,EAAQ,GAA9B1C,EAAGkD,EAAA,GAAE/E,EAAK+E,EAAA,GAAEjD,QAER,IAATD,EACF,MAAA,CAAA,EAAOmB,EAAS,EAAG,cAAehD,EAAO6B,EAAKC,IAMhD,IAHMkD,EAAQvL,EAAWmF,EAAcE,EACnCiD,EAAO,EAEFzD,EAAI,EAAGA,EAAI0G,EAAMxI,OAAQ8B,IAC5BuD,GAAOmD,EAAM1G,KACfyD,EAAOzD,GAIX,MAAA,CAAA,EAAO0E,EAASjB,EAAM,YAAa/B,EAAO6B,EAAKC", "x_google_ignoreList": [2]}