function e(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{d(n.next(e))}catch(e){a(e)}}function c(e){try{d(n.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,c)}d((n=n.apply(e,t||[])).next())}))}"function"==typeof SuppressedError&&SuppressedError;const t=["geforce 320m","geforce 8600","geforce 8600m gt","geforce 8800 gs","geforce 8800 gt","geforce 9400","geforce 9400m g","geforce 9400m","geforce 9600m gt","geforce 9600m","geforce fx go5200","geforce gt 120","geforce gt 130","geforce gt 330m","geforce gtx 285","google swiftshader","intel g41","intel g45","intel gma 4500mhd","intel gma x3100","intel hd 3000","intel q45","legacy","mali-2","mali-3","mali-4","quadro fx 1500","quadro fx 4","quadro fx 5","radeon hd 2400","radeon hd 2600","radeon hd 4670","radeon hd 4850","radeon hd 4870","radeon hd 5670","radeon hd 5750","radeon hd 6290","radeon hd 6300","radeon hd 6310","radeon hd 6320","radeon hd 6490m","radeon hd 6630m","radeon hd 6750m","radeon hd 6770m","radeon hd 6970m","sgx 543","sgx543"];function r(e){return e=e.toLowerCase().replace(/.*angle ?\((.+)\)(?: on vulkan [0-9.]+)?$/i,"$1").replace(/\s(\d{1,2}gb|direct3d.+$)|\(r\)| \([^)]+\)$/g,"").replace(/(?:vulkan|opengl) \d+\.\d+(?:\.\d+)?(?: \((.*)\))?/,"$1")}const n="undefined"==typeof window,o=(()=>{if(n)return;const{userAgent:e,platform:t,maxTouchPoints:r}=window.navigator,o=/(iphone|ipod|ipad)/i.test(e),a="iPad"===t||"MacIntel"===t&&r>0&&!window.MSStream;return{isIpad:a,isMobile:/android/i.test(e)||o||a,isSafari12:/Version\/12.+Safari/.test(e),isFirefox:/Firefox/.test(e)}})();function a(e,t,r){if(!r)return[t];const n=function(e){const t="\n    precision highp float;\n    attribute vec3 aPosition;\n    varying float vvv;\n    void main() {\n      vvv = 0.31622776601683794;\n      gl_Position = vec4(aPosition, 1.0);\n    }\n  ",r="\n    precision highp float;\n    varying float vvv;\n    void main() {\n      vec4 enc = vec4(1.0, 255.0, 65025.0, 16581375.0) * vvv;\n      enc = fract(enc);\n      enc -= enc.yzww * vec4(1.0 / 255.0, 1.0 / 255.0, 1.0 / 255.0, 0.0);\n      gl_FragColor = enc;\n    }\n  ",n=e.createShader(35633),o=e.createShader(35632),a=e.createProgram();if(!(o&&n&&a))return;e.shaderSource(n,t),e.shaderSource(o,r),e.compileShader(n),e.compileShader(o),e.attachShader(a,n),e.attachShader(a,o),e.linkProgram(a),e.detachShader(a,n),e.detachShader(a,o),e.deleteShader(n),e.deleteShader(o),e.useProgram(a);const i=e.createBuffer();e.bindBuffer(34962,i),e.bufferData(34962,new Float32Array([-1,-1,0,3,-1,0,-1,3,0]),35044);const c=e.getAttribLocation(a,"aPosition");e.vertexAttribPointer(c,3,5126,!1,0,0),e.enableVertexAttribArray(c),e.clearColor(1,1,1,1),e.clear(16384),e.viewport(0,0,1,1),e.drawArrays(4,0,3);const d=new Uint8Array(4);return e.readPixels(0,0,1,1,6408,5121,d),e.deleteProgram(a),e.deleteBuffer(i),d.join("")}(e),a="801621810",i="8016218135",c="80162181161",d=(null==o?void 0:o.isIpad)?[["a7",c,12],["a8",i,15],["a8x",i,15],["a9",i,15],["a9x",i,15],["a10",i,15],["a10x",i,15],["a12",a,15],["a12x",a,15],["a12z",a,15],["a14",a,15],["a15",a,15],["m1",a,15],["m2",a,15]]:[["a7",c,12],["a8",i,12],["a9",i,15],["a10",i,15],["a11",a,15],["a12",a,15],["a13",a,15],["a14",a,15],["a15",a,15],["a16",a,15],["a17",a,15]];let l;"80162181255"===n?l=d.filter((([,,e])=>e>=14)):(l=d.filter((([,e])=>e===n)),l.length||(l=d));return l.map((([e])=>`apple ${e} gpu`))}class i extends Error{constructor(e){super(e),Object.setPrototypeOf(this,new.target.prototype)}}const c=[],d=[];function l(e,t){if(e===t)return 0;const r=e;e.length>t.length&&(e=t,t=r);let n=e.length,o=t.length;for(;n>0&&e.charCodeAt(~-n)===t.charCodeAt(~-o);)n--,o--;let a,i=0;for(;i<n&&e.charCodeAt(i)===t.charCodeAt(i);)i++;if(n-=i,o-=i,0===n)return o;let l,s,f=0,u=0,g=0;for(;u<n;)d[u]=e.charCodeAt(i+u),c[u]=++u;for(;g<o;)for(a=t.charCodeAt(i+g),l=g++,f=g,u=0;u<n;u++)s=a===d[u]?l:l+1,l=c[u],f=c[u]=l>f?s>f?f+1:s:s>l?l+1:s;return f}function s(e){return null!=e}const f=({mobileTiers:c=[0,15,30,60],desktopTiers:d=[0,15,30,60],override:f={},glContext:u,failIfMajorPerformanceCaveat:g=!1,benchmarksURL:h="https://unpkg.com/detect-gpu@5.0.70/dist/benchmarks"}={})=>e(void 0,void 0,void 0,(function*(){const p={};if(n)return{tier:0,type:"SSR"};const{isIpad:m=!!(null==o?void 0:o.isIpad),isMobile:v=!!(null==o?void 0:o.isMobile),screenSize:w=window.screen,loadBenchmarks:x=(t=>e(void 0,void 0,void 0,(function*(){const e=yield fetch(`${h}/${t}`).then((e=>e.json()));if(parseInt(e.shift().split(".")[0],10)<4)throw new i("Detect GPU benchmark data is out of date. Please update to version 4x");return e})))}=f;let{renderer:A}=f;const P=(e,t,r,n,o)=>({device:o,fps:n,gpu:r,isMobile:v,tier:e,type:t});let S,b="";if(A)A=r(A),S=[A];else{const e=u||function(e,t=!1){const r={alpha:!1,antialias:!1,depth:!1,failIfMajorPerformanceCaveat:t,powerPreference:"high-performance",stencil:!1};e&&delete r.powerPreference;const n=window.document.createElement("canvas"),o=n.getContext("webgl",r)||n.getContext("experimental-webgl",r);return null!=o?o:void 0}(null==o?void 0:o.isSafari12,g);if(!e)return P(0,"WEBGL_UNSUPPORTED");const t=(null==o?void 0:o.isFirefox)?null:e.getExtension("WEBGL_debug_renderer_info");if(A=t?e.getParameter(t.UNMASKED_RENDERER_WEBGL):e.getParameter(e.RENDERER),!A)return P(1,"FALLBACK");b=A,A=r(A),S=function(e,t,r){return"apple gpu"===t?a(e,t,r):[t]}(e,A,v)}const E=(yield Promise.all(S.map((function(t){var r;return e(this,void 0,void 0,(function*(){const e=(e=>{const t=v?["adreno","apple","mali-t","mali","nvidia","powervr","samsung"]:["intel","apple","amd","radeon","nvidia","geforce","adreno"];for(const r of t)if(e.includes(r))return r})(t);if(!e)return;const n=`${v?"m":"d"}-${e}${m?"-ipad":""}.json`,o=p[n]=null!==(r=p[n])&&void 0!==r?r:x(n);let a;try{a=yield o}catch(e){if(e instanceof i)throw e;return}const c=function(e){var t;const r=(e=e.replace(/\([^)]+\)/,"")).match(/\d+/)||e.match(/(\W|^)([A-Za-z]{1,3})(\W|$)/g);return null!==(t=null==r?void 0:r.join("").replace(/\W|amd/g,""))&&void 0!==t?t:""}(t);let d=a.filter((([,e])=>e===c));d.length||(d=a.filter((([e])=>e.includes(t))));const s=d.length;if(0===s)return;const f=t.split(/[.,()\[\]/\s]/g).sort().filter(((e,t,r)=>0===t||e!==r[t-1])).join(" ");let u,[g,,,,h]=s>1?d.map((e=>[e,l(f,e[2])])).sort((([,e],[,t])=>e-t))[0][0]:d[0],A=Number.MAX_VALUE;const{devicePixelRatio:P}=window,S=w.width*P*w.height*P;for(const e of h){const[t,r]=e,n=t*r,o=Math.abs(S-n);o<A&&(A=o,u=e)}if(!u)return;const[,,b,E]=u;return[A,b,g,E]}))})))).filter(s).sort((([e=Number.MAX_VALUE,t],[r=Number.MAX_VALUE,n])=>e===r?t-n:e-r));if(!E.length){const e=t.find((e=>A.includes(e)));return e?P(0,"BLOCKLISTED",e):P(1,"FALLBACK",`${A} (${b})`)}const[,y,C,L]=E[0];if(-1===y)return P(0,"BLOCKLISTED",C,y,L);const M=v?c:d;let $=0;for(let e=0;e<M.length;e++)y>=M[e]&&($=e);return P($,"BENCHMARK",C,y,L)}));export{f as getGPUTier};
//# sourceMappingURL=detect-gpu.esm.js.map
