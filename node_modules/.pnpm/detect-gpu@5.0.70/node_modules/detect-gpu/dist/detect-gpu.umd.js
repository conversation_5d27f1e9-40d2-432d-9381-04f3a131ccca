!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).DetectGPU={})}(this,(function(e){"use strict";var r=function(e,n){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])})(e,n)};function n(e,r,n,t){return new(n||(n=Promise))((function(o,a){function i(e){try{u(t.next(e))}catch(e){a(e)}}function c(e){try{u(t.throw(e))}catch(e){a(e)}}function u(e){var r;e.done?o(e.value):(r=e.value,r instanceof n?r:new n((function(e){e(r)}))).then(i,c)}u((t=t.apply(e,r||[])).next())}))}function t(e,r){var n,t,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(u){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(i=0)),i;)try{if(n=1,t&&(o=2&c[0]?t.return:c[0]?t.throw||((o=t.return)&&o.call(t),0):t.next)&&!(o=o.call(t,c[1])).done)return o;switch(t=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return i.label++,{value:c[1],done:!1};case 5:i.label++,t=c[1],c=[0];continue;case 7:c=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){i=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){i.label=c[1];break}if(6===c[0]&&i.label<o[1]){i.label=o[1],o=c;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(c);break}o[2]&&i.ops.pop(),i.trys.pop();continue}c=r.call(e,i)}catch(e){c=[6,e],t=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}}"function"==typeof SuppressedError&&SuppressedError;var o=["geforce 320m","geforce 8600","geforce 8600m gt","geforce 8800 gs","geforce 8800 gt","geforce 9400","geforce 9400m g","geforce 9400m","geforce 9600m gt","geforce 9600m","geforce fx go5200","geforce gt 120","geforce gt 130","geforce gt 330m","geforce gtx 285","google swiftshader","intel g41","intel g45","intel gma 4500mhd","intel gma x3100","intel hd 3000","intel q45","legacy","mali-2","mali-3","mali-4","quadro fx 1500","quadro fx 4","quadro fx 5","radeon hd 2400","radeon hd 2600","radeon hd 4670","radeon hd 4850","radeon hd 4870","radeon hd 5670","radeon hd 5750","radeon hd 6290","radeon hd 6300","radeon hd 6310","radeon hd 6320","radeon hd 6490m","radeon hd 6630m","radeon hd 6750m","radeon hd 6770m","radeon hd 6970m","sgx 543","sgx543"];function a(e){return e=e.toLowerCase().replace(/.*angle ?\((.+)\)(?: on vulkan [0-9.]+)?$/i,"$1").replace(/\s(\d{1,2}gb|direct3d.+$)|\(r\)| \([^)]+\)$/g,"").replace(/(?:vulkan|opengl) \d+\.\d+(?:\.\d+)?(?: \((.*)\))?/,"$1")}const i=34962;var c="undefined"==typeof window,u=function(){if(!c){var e=window.navigator,r=e.userAgent,n=e.platform,t=e.maxTouchPoints,o=/(iphone|ipod|ipad)/i.test(r),a="iPad"===n||"MacIntel"===n&&t>0&&!window.MSStream;return{isIpad:a,isMobile:/android/i.test(r)||o||a,isSafari12:/Version\/12.+Safari/.test(r),isFirefox:/Firefox/.test(r)}}}();function l(e,r,n){if(!n)return[r];var t,o=function(e){var r="\n    precision highp float;\n    attribute vec3 aPosition;\n    varying float vvv;\n    void main() {\n      vvv = 0.31622776601683794;\n      gl_Position = vec4(aPosition, 1.0);\n    }\n  ",n="\n    precision highp float;\n    varying float vvv;\n    void main() {\n      vec4 enc = vec4(1.0, 255.0, 65025.0, 16581375.0) * vvv;\n      enc = fract(enc);\n      enc -= enc.yzww * vec4(1.0 / 255.0, 1.0 / 255.0, 1.0 / 255.0, 0.0);\n      gl_FragColor = enc;\n    }\n  ",t=e.createShader(35633),o=e.createShader(35632),a=e.createProgram();if(!(o&&t&&a))return;e.shaderSource(t,r),e.shaderSource(o,n),e.compileShader(t),e.compileShader(o),e.attachShader(a,t),e.attachShader(a,o),e.linkProgram(a),e.detachShader(a,t),e.detachShader(a,o),e.deleteShader(t),e.deleteShader(o),e.useProgram(a);var c=e.createBuffer();e.bindBuffer(i,c),e.bufferData(i,new Float32Array([-1,-1,0,3,-1,0,-1,3,0]),35044);var u=e.getAttribLocation(a,"aPosition");e.vertexAttribPointer(u,3,5126,!1,0,0),e.enableVertexAttribArray(u),e.clearColor(1,1,1,1),e.clear(16384),e.viewport(0,0,1,1),e.drawArrays(4,0,3);var l=new Uint8Array(4);return e.readPixels(0,0,1,1,6408,5121,l),e.deleteProgram(a),e.deleteBuffer(c),l.join("")}(e),a="801621810",c="8016218135",l="80162181161",f=(null==u?void 0:u.isIpad)?[["a7",l,12],["a8",c,15],["a8x",c,15],["a9",c,15],["a9x",c,15],["a10",c,15],["a10x",c,15],["a12",a,15],["a12x",a,15],["a12z",a,15],["a14",a,15],["a15",a,15],["m1",a,15],["m2",a,15]]:[["a7",l,12],["a8",c,12],["a9",c,15],["a10",c,15],["a11",a,15],["a12",a,15],["a13",a,15],["a14",a,15],["a15",a,15],["a16",a,15],["a17",a,15]];return"80162181255"===o?t=f.filter((function(e){return e[2]>=14})):(t=f.filter((function(e){return e[1]===o}))).length||(t=f),t.map((function(e){var r=e[0];return"apple ".concat(r," gpu")}))}var f=function(e){function n(r){var n=this.constructor,t=e.call(this,r)||this;return Object.setPrototypeOf(t,n.prototype),t}return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function t(){this.constructor=e}r(e,n),e.prototype=null===n?Object.create(n):(t.prototype=n.prototype,new t)}(n,e),n}(Error);var d=[],s=[];function h(e,r){if(e===r)return 0;var n=e;e.length>r.length&&(e=r,r=n);for(var t=e.length,o=r.length;t>0&&e.charCodeAt(~-t)===r.charCodeAt(~-o);)t--,o--;for(var a,i=0;i<t&&e.charCodeAt(i)===r.charCodeAt(i);)i++;if(o-=i,0===(t-=i))return o;for(var c,u,l=0,f=0,h=0;f<t;)s[f]=e.charCodeAt(i+f),d[f]=++f;for(;h<o;)for(a=r.charCodeAt(i+h),c=h++,l=h,f=0;f<t;f++)u=a===s[f]?c:c+1,c=d[f],l=d[f]=c>l?u>l?l+1:u:u>c?c+1:u;return l}function v(e){return null!=e}e.getGPUTier=function(e){var r=void 0===e?{}:e,i=r.mobileTiers,d=void 0===i?[0,15,30,60]:i,s=r.desktopTiers,p=void 0===s?[0,15,30,60]:s,g=r.override,m=void 0===g?{}:g,b=r.glContext,w=r.failIfMajorPerformanceCaveat,y=void 0!==w&&w,x=r.benchmarksURL,P=void 0===x?"https://unpkg.com/detect-gpu@".concat("5.0.70","/dist/benchmarks"):x;return n(void 0,void 0,void 0,(function(){function e(e){var o;return n(this,void 0,void 0,(function(){var n,a,i,c,u,l,d,v,p,g,m,b,y,x,P,A,C,L,M,k,j,B,R,T,U,I;return t(this,(function(t){switch(t.label){case 0:if(!(n=_(e)))return[2];a="".concat(w?"m":"d","-").concat(n).concat(s?"-ipad":"",".json"),i=r[a]=null!==(o=r[a])&&void 0!==o?o:E(a),t.label=1;case 1:return t.trys.push([1,3,,4]),[4,i];case 2:return c=t.sent(),[3,4];case 3:if((u=t.sent())instanceof f)throw u;return[2];case 4:if(l=function(e){var r,n=(e=e.replace(/\([^)]+\)/,"")).match(/\d+/)||e.match(/(\W|^)([A-Za-z]{1,3})(\W|$)/g);return null!==(r=null==n?void 0:n.join("").replace(/\W|amd/g,""))&&void 0!==r?r:""}(e),(d=c.filter((function(e){return e[1]===l}))).length||(d=c.filter((function(r){return r[0].includes(e)}))),0===(v=d.length))return[2];for(p=e.split(/[.,()\[\]/\s]/g).sort().filter((function(e,r,n){return 0===r||e!==n[r-1]})).join(" "),g=v>1?d.map((function(e){return[e,h(p,e[2])]})).sort((function(e,r){return e[1]-r[1]}))[0][0]:d[0],m=g[0],b=g[4],y=Number.MAX_VALUE,P=window.devicePixelRatio,A=S.width*P*S.height*P,C=0,L=b;C<L.length;C++)M=L[C],k=M[0],j=M[1],B=k*j,(R=Math.abs(A-B))<y&&(y=R,x=M);return x?(U=(T=x)[2],I=T[3],[2,[y,U,m,I]]):[2]}}))}))}var r,i,s,g,w,x,S,A,E,C,_,L,M,k,j,B,R,T,U,I,O,D,N,F,G;return t(this,(function(h){switch(h.label){case 0:if(r={},c)return[2,{tier:0,type:"SSR"}];if(i=m.isIpad,s=void 0===i?!!(null==u?void 0:u.isIpad):i,g=m.isMobile,w=void 0===g?!!(null==u?void 0:u.isMobile):g,x=m.screenSize,S=void 0===x?window.screen:x,A=m.loadBenchmarks,E=void 0===A?function(e){return n(void 0,void 0,void 0,(function(){var r;return t(this,(function(n){switch(n.label){case 0:return[4,fetch("".concat(P,"/").concat(e)).then((function(e){return e.json()}))];case 1:if(r=n.sent(),parseInt(r.shift().split(".")[0],10)<4)throw new f("Detect GPU benchmark data is out of date. Please update to version 4x");return[2,r]}}))}))}:A,C=m.renderer,_=function(e){for(var r=0,n=w?["adreno","apple","mali-t","mali","nvidia","powervr","samsung"]:["intel","apple","amd","radeon","nvidia","geforce","adreno"];r<n.length;r++){var t=n[r];if(e.includes(t))return t}},L=function(e,r,n,t,o){return{device:o,fps:t,gpu:n,isMobile:w,tier:e,type:r}},k="",C)C=a(C),M=[C];else{if(!(j=b||function(e,r){void 0===r&&(r=!1);var n={alpha:!1,antialias:!1,depth:!1,failIfMajorPerformanceCaveat:r,powerPreference:"high-performance",stencil:!1};e&&delete n.powerPreference;var t=window.document.createElement("canvas"),o=t.getContext("webgl",n)||t.getContext("experimental-webgl",n);return null!=o?o:void 0}(null==u?void 0:u.isSafari12,y)))return[2,L(0,"WEBGL_UNSUPPORTED")];if(B=(null==u?void 0:u.isFirefox)?null:j.getExtension("WEBGL_debug_renderer_info"),!(C=B?j.getParameter(B.UNMASKED_RENDERER_WEBGL):j.getParameter(j.RENDERER)))return[2,L(1,"FALLBACK")];k=C,C=a(C),M=function(e,r,n){return"apple gpu"===r?l(e,r,n):[r]}(j,C,w)}return[4,Promise.all(M.map(e))];case 1:if(!(R=h.sent().filter(v).sort((function(e,r){var n=e[0],t=void 0===n?Number.MAX_VALUE:n,o=e[1],a=r[0],i=void 0===a?Number.MAX_VALUE:a,c=r[1];return t===i?o-c:t-i}))).length)return[2,(T=o.find((function(e){return C.includes(e)})))?L(0,"BLOCKLISTED",T):L(1,"FALLBACK","".concat(C," (").concat(k,")"))];if(U=R[0],I=U[1],O=U[2],D=U[3],-1===I)return[2,L(0,"BLOCKLISTED",O,I,D)];for(N=w?d:p,F=0,G=0;G<N.length;G++)I>=N[G]&&(F=G);return[2,L(F,"BENCHMARK",O,I,D)]}}))}))}}));
//# sourceMappingURL=detect-gpu.umd.js.map
